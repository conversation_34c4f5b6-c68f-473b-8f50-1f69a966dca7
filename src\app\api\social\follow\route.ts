import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { z } from 'zod'

const followSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
})

// POST /api/social/follow - Follow a user
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { userId } = followSchema.parse(body)

    // Can't follow yourself
    if (userId === session.user.id) {
      return NextResponse.json(
        { error: 'Cannot follow yourself' },
        { status: 400 }
      )
    }

    // Check if user exists
    const userToFollow = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, username: true, name: true }
    })

    if (!userToFollow) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Check if already following
    const existingFollow = await prisma.follow.findUnique({
      where: {
        followerId_followingId: {
          followerId: session.user.id,
          followingId: userId
        }
      }
    })

    if (existingFollow) {
      return NextResponse.json(
        { error: 'Already following this user' },
        { status: 400 }
      )
    }

    // Create follow relationship
    const follow = await prisma.follow.create({
      data: {
        followerId: session.user.id,
        followingId: userId
      },
      include: {
        following: {
          select: {
            id: true,
            username: true,
            name: true,
            avatar: true,
            verified: true
          }
        }
      }
    })

    // Create notification for the followed user
    await prisma.notification.create({
      data: {
        userId: userId,
        type: 'NEW_FOLLOWER',
        title: 'New Follower',
        message: `${session.user.name || session.user.username} started following you`,
        data: {
          followerId: session.user.id,
          followerName: session.user.name,
          followerUsername: session.user.username
        }
      }
    })

    return NextResponse.json({
      message: 'Successfully followed user',
      follow,
      isFollowing: true
    }, { status: 201 })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error following user:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/social/follow - Unfollow a user
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // Find and delete follow relationship
    const follow = await prisma.follow.findUnique({
      where: {
        followerId_followingId: {
          followerId: session.user.id,
          followingId: userId
        }
      }
    })

    if (!follow) {
      return NextResponse.json(
        { error: 'Not following this user' },
        { status: 400 }
      )
    }

    await prisma.follow.delete({
      where: {
        followerId_followingId: {
          followerId: session.user.id,
          followingId: userId
        }
      }
    })

    return NextResponse.json({
      message: 'Successfully unfollowed user',
      isFollowing: false
    })

  } catch (error) {
    console.error('Error unfollowing user:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET /api/social/follow - Get follow relationships
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') // 'followers' or 'following'
    const userId = searchParams.get('userId') || session.user.id
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')

    const skip = (page - 1) * limit

    let data, total

    if (type === 'followers') {
      // Get users who follow the specified user
      [data, total] = await Promise.all([
        prisma.follow.findMany({
          where: { followingId: userId },
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            follower: {
              select: {
                id: true,
                username: true,
                name: true,
                avatar: true,
                verified: true,
                _count: {
                  select: {
                    followers: true,
                    trades: true
                  }
                }
              }
            }
          }
        }),
        prisma.follow.count({ where: { followingId: userId } })
      ])

      data = data.map(follow => follow.follower)
    } else {
      // Get users that the specified user follows
      [data, total] = await Promise.all([
        prisma.follow.findMany({
          where: { followerId: userId },
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            following: {
              select: {
                id: true,
                username: true,
                name: true,
                avatar: true,
                verified: true,
                _count: {
                  select: {
                    followers: true,
                    trades: true
                  }
                }
              }
            }
          }
        }),
        prisma.follow.count({ where: { followerId: userId } })
      ])

      data = data.map(follow => follow.following)
    }

    return NextResponse.json({
      users: data,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      }
    })

  } catch (error) {
    console.error('Error fetching follow relationships:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
