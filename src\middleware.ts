import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'
import { rateLimit, rateLimitConfigs } from '@/lib/rate-limit'

// Define protected routes that require authentication
const protectedRoutes = [
  '/dashboard',
  '/trades',
  '/analytics',
  '/profile',
  '/settings',
  '/api/trades',
  '/api/analytics',
  '/api/social',
  '/api/users/me',
]

// Define public routes that don't require authentication
const publicRoutes = [
  '/',
  '/auth/signin',
  '/auth/signup',
  '/auth/error',
  '/api/auth',
  '/api/market',
  '/about',
  '/blog',
  '/help',
]

// Rate limiting configuration for different route patterns
const routeRateLimits: Record<string, keyof typeof rateLimitConfigs> = {
  '/api/auth': 'auth',
  '/api/market': 'market',
  '/api/social': 'social',
  '/api/trades': 'trades',
  '/api/upload': 'upload',
  // Default for other API routes
  '/api': 'api',
}

function getRouteRateLimitType(pathname: string): keyof typeof rateLimitConfigs {
  // Find the most specific matching route
  const matchingRoute = Object.keys(routeRateLimits)
    .sort((a, b) => b.length - a.length) // Sort by length (most specific first)
    .find(route => pathname.startsWith(route))
  
  return matchingRoute ? routeRateLimits[matchingRoute] : 'api'
}

function isProtectedRoute(pathname: string): boolean {
  return protectedRoutes.some(route => pathname.startsWith(route))
}

function isPublicRoute(pathname: string): boolean {
  return publicRoutes.some(route => pathname === route || pathname.startsWith(route))
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // Skip middleware for static files and Next.js internals
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/static') ||
    pathname.includes('.') ||
    pathname === '/favicon.ico'
  ) {
    return NextResponse.next()
  }

  // Apply rate limiting to API routes
  if (pathname.startsWith('/api')) {
    try {
      const rateLimitType = getRouteRateLimitType(pathname)
      
      // Get user ID from token if available
      let userId: string | undefined
      try {
        const token = await getToken({ 
          req: request, 
          secret: process.env.NEXTAUTH_SECRET 
        })
        userId = token?.id as string
      } catch (error) {
        // Continue without user ID
      }

      const rateLimitResult = await rateLimit(request, rateLimitType, userId)

      if (!rateLimitResult.success) {
        const response = NextResponse.json(
          { 
            error: rateLimitResult.error,
            retryAfter: Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000)
          },
          { status: 429 }
        )

        // Add rate limit headers
        response.headers.set('X-RateLimit-Limit', rateLimitResult.limit.toString())
        response.headers.set('X-RateLimit-Remaining', rateLimitResult.remaining.toString())
        response.headers.set('X-RateLimit-Reset', rateLimitResult.resetTime.toString())
        response.headers.set('Retry-After', Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000).toString())

        return response
      }

      // Add rate limit headers to successful requests
      const response = NextResponse.next()
      response.headers.set('X-RateLimit-Limit', rateLimitResult.limit.toString())
      response.headers.set('X-RateLimit-Remaining', rateLimitResult.remaining.toString())
      response.headers.set('X-RateLimit-Reset', rateLimitResult.resetTime.toString())

      return response
    } catch (error) {
      console.error('Rate limiting middleware error:', error)
      // Continue without rate limiting on error
    }
  }

  // Authentication check for protected routes
  if (isProtectedRoute(pathname)) {
    try {
      const token = await getToken({ 
        req: request, 
        secret: process.env.NEXTAUTH_SECRET 
      })

      if (!token) {
        // Redirect to signin page for web routes
        if (!pathname.startsWith('/api')) {
          const signInUrl = new URL('/auth/signin', request.url)
          signInUrl.searchParams.set('callbackUrl', request.url)
          return NextResponse.redirect(signInUrl)
        }

        // Return 401 for API routes
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        )
      }
    } catch (error) {
      console.error('Authentication middleware error:', error)
      
      // Redirect to signin on auth error for web routes
      if (!pathname.startsWith('/api')) {
        const signInUrl = new URL('/auth/signin', request.url)
        signInUrl.searchParams.set('callbackUrl', request.url)
        return NextResponse.redirect(signInUrl)
      }

      return NextResponse.json(
        { error: 'Authentication error' },
        { status: 401 }
      )
    }
  }

  // Redirect authenticated users away from auth pages
  if (pathname.startsWith('/auth/')) {
    try {
      const token = await getToken({ 
        req: request, 
        secret: process.env.NEXTAUTH_SECRET 
      })

      if (token) {
        return NextResponse.redirect(new URL('/dashboard', request.url))
      }
    } catch (error) {
      // Continue to auth page on error
    }
  }

  // Add security headers
  const response = NextResponse.next()
  
  // Security headers
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  response.headers.set('X-XSS-Protection', '1; mode=block')
  
  // CSP header for additional security
  const cspHeader = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://vercel.live",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https: blob:",
    "font-src 'self' data:",
    "connect-src 'self' https: wss:",
    "frame-src 'self' https:",
  ].join('; ')
  
  response.headers.set('Content-Security-Policy', cspHeader)

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
}
