import { authOptions } from '../../src/lib/auth'
import bcrypt from 'bcryptjs'

// Mock dependencies
jest.mock('../../src/lib/db', () => ({
  prisma: {
    user: {
      findUnique: jest.fn(),
      create: jest.fn(),
    },
  },
}))

jest.mock('bcryptjs', () => ({
  compare: jest.fn(),
  hash: jest.fn(),
}))

const mockBcrypt = bcrypt as jest.Mocked<typeof bcrypt>

describe('Authentication', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Credentials Provider', () => {
    it('should authenticate user with valid credentials', async () => {
      const { prisma } = require('../../src/lib/db')
      
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        username: 'testuser',
        name: 'Test User',
        password: 'hashed_password',
        verified: true,
      }

      prisma.user.findUnique.mockResolvedValue(mockUser)
      mockBcrypt.compare.mockResolvedValue(true)

      const credentialsProvider = authOptions.providers?.find(
        (provider) => provider.id === 'credentials'
      )

      if (!credentialsProvider || !('authorize' in credentialsProvider)) {
        throw new Error('Credentials provider not found')
      }

      const result = await credentialsProvider.authorize({
        email: '<EMAIL>',
        password: 'password123',
      })

      expect(result).toEqual({
        id: 'user123',
        email: '<EMAIL>',
        name: 'Test User',
        username: 'testuser',
        avatar: undefined,
        verified: true,
      })
      expect(prisma.user.findUnique).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
      })
      expect(mockBcrypt.compare).toHaveBeenCalledWith('password123', 'hashed_password')
    })

    it('should reject authentication with invalid email', async () => {
      const { prisma } = require('../../src/lib/db')
      
      prisma.user.findUnique.mockResolvedValue(null)

      const credentialsProvider = authOptions.providers?.find(
        (provider) => provider.id === 'credentials'
      )

      if (!credentialsProvider || !('authorize' in credentialsProvider)) {
        throw new Error('Credentials provider not found')
      }

      const result = await credentialsProvider.authorize({
        email: '<EMAIL>',
        password: 'password123',
      })

      expect(result).toBeNull()
      expect(prisma.user.findUnique).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
      })
    })

    it('should reject authentication with invalid password', async () => {
      const { prisma } = require('../../src/lib/db')
      
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        password: 'hashed_password',
      }

      prisma.user.findUnique.mockResolvedValue(mockUser)
      mockBcrypt.compare.mockResolvedValue(false)

      const credentialsProvider = authOptions.providers?.find(
        (provider) => provider.id === 'credentials'
      )

      if (!credentialsProvider || !('authorize' in credentialsProvider)) {
        throw new Error('Credentials provider not found')
      }

      const result = await credentialsProvider.authorize({
        email: '<EMAIL>',
        password: 'wrongpassword',
      })

      expect(result).toBeNull()
      expect(mockBcrypt.compare).toHaveBeenCalledWith('wrongpassword', 'hashed_password')
    })

    it('should reject authentication with missing credentials', async () => {
      const credentialsProvider = authOptions.providers?.find(
        (provider) => provider.id === 'credentials'
      )

      if (!credentialsProvider || !('authorize' in credentialsProvider)) {
        throw new Error('Credentials provider not found')
      }

      // Test missing email
      const result1 = await credentialsProvider.authorize({
        password: 'password123',
      })
      expect(result1).toBeNull()

      // Test missing password
      const result2 = await credentialsProvider.authorize({
        email: '<EMAIL>',
      })
      expect(result2).toBeNull()

      // Test missing both
      const result3 = await credentialsProvider.authorize({})
      expect(result3).toBeNull()
    })

    it('should reject authentication for user without password', async () => {
      const { prisma } = require('../../src/lib/db')
      
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        password: null, // OAuth user without password
      }

      prisma.user.findUnique.mockResolvedValue(mockUser)

      const credentialsProvider = authOptions.providers?.find(
        (provider) => provider.id === 'credentials'
      )

      if (!credentialsProvider || !('authorize' in credentialsProvider)) {
        throw new Error('Credentials provider not found')
      }

      const result = await credentialsProvider.authorize({
        email: '<EMAIL>',
        password: 'password123',
      })

      expect(result).toBeNull()
    })
  })

  describe('JWT Callback', () => {
    it('should add user data to JWT token', async () => {
      const mockUser = {
        id: 'user123',
        username: 'testuser',
        verified: true,
      }

      const token = {}
      const result = await authOptions.callbacks?.jwt?.({
        token,
        user: mockUser,
        account: null,
      })

      expect(result).toEqual({
        username: 'testuser',
        verified: true,
      })
    })

    it('should handle OAuth account linking', async () => {
      const { prisma } = require('../../src/lib/db')
      
      const existingUser = {
        id: 'user123',
        username: 'testuser',
        verified: true,
      }

      prisma.user.findUnique.mockResolvedValue(existingUser)

      const token = { email: '<EMAIL>' }
      const account = { provider: 'google' }

      const result = await authOptions.callbacks?.jwt?.({
        token,
        user: null,
        account,
      })

      expect(result).toEqual({
        email: '<EMAIL>',
        id: 'user123',
        username: 'testuser',
        verified: true,
      })
      expect(prisma.user.findUnique).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
      })
    })
  })

  describe('Session Callback', () => {
    it('should add user data to session', async () => {
      const session = {
        user: { email: '<EMAIL>' },
      }
      const token = {
        id: 'user123',
        username: 'testuser',
        verified: true,
      }

      const result = await authOptions.callbacks?.session?.({ session, token })

      expect(result).toEqual({
        user: {
          email: '<EMAIL>',
          id: 'user123',
          username: 'testuser',
          verified: true,
        },
      })
    })
  })

  describe('SignIn Callback', () => {
    it('should create new user for OAuth sign-in', async () => {
      const { prisma } = require('../../src/lib/db')
      
      prisma.user.findUnique.mockResolvedValue(null) // No existing user
      prisma.user.create.mockResolvedValue({
        id: 'user123',
        email: '<EMAIL>',
        username: 'test',
      })

      const user = {
        email: '<EMAIL>',
        name: 'Test User',
        image: 'https://example.com/avatar.jpg',
      }
      const account = { provider: 'google' }

      const result = await authOptions.callbacks?.signIn?.({ user, account, profile: null })

      expect(result).toBe(true)
      expect(prisma.user.create).toHaveBeenCalledWith({
        data: {
          email: '<EMAIL>',
          name: 'Test User',
          username: 'test',
          avatar: 'https://example.com/avatar.jpg',
          emailVerified: expect.any(Date),
        },
      })
    })

    it('should handle existing user for OAuth sign-in', async () => {
      const { prisma } = require('../../src/lib/db')
      
      const existingUser = {
        id: 'user123',
        email: '<EMAIL>',
        username: 'testuser',
      }
      prisma.user.findUnique.mockResolvedValue(existingUser)

      const user = {
        email: '<EMAIL>',
        name: 'Test User',
      }
      const account = { provider: 'google' }

      const result = await authOptions.callbacks?.signIn?.({ user, account, profile: null })

      expect(result).toBe(true)
      expect(prisma.user.create).not.toHaveBeenCalled()
    })

    it('should generate unique username when username exists', async () => {
      const { prisma } = require('../../src/lib/db')
      
      prisma.user.findUnique
        .mockResolvedValueOnce(null) // No user with email
        .mockResolvedValueOnce({ id: 'other', username: 'test' }) // Username exists
      
      prisma.user.create.mockResolvedValue({
        id: 'user123',
        email: '<EMAIL>',
      })

      const user = {
        email: '<EMAIL>',
        name: 'Test User',
      }
      const account = { provider: 'google' }

      const result = await authOptions.callbacks?.signIn?.({ user, account, profile: null })

      expect(result).toBe(true)
      expect(prisma.user.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          username: expect.stringMatching(/^test_[a-z0-9]+$/),
        }),
      })
    })

    it('should allow credentials sign-in', async () => {
      const user = { email: '<EMAIL>' }
      const account = { provider: 'credentials' }

      const result = await authOptions.callbacks?.signIn?.({ user, account, profile: null })

      expect(result).toBe(true)
    })

    it('should handle sign-in errors gracefully', async () => {
      const { prisma } = require('../../src/lib/db')
      
      prisma.user.findUnique.mockRejectedValue(new Error('Database error'))

      const user = { email: '<EMAIL>' }
      const account = { provider: 'google' }

      const result = await authOptions.callbacks?.signIn?.({ user, account, profile: null })

      expect(result).toBe(false)
    })
  })

  describe('Auth Configuration', () => {
    it('should have correct session configuration', () => {
      expect(authOptions.session?.strategy).toBe('jwt')
      expect(authOptions.session?.maxAge).toBe(30 * 24 * 60 * 60) // 30 days
    })

    it('should have correct page configuration', () => {
      expect(authOptions.pages?.signIn).toBe('/auth/signin')
      expect(authOptions.pages?.signUp).toBe('/auth/signup')
      expect(authOptions.pages?.error).toBe('/auth/error')
    })

    it('should have debug enabled in development', () => {
      const originalEnv = process.env.NODE_ENV
      
      process.env.NODE_ENV = 'development'
      expect(authOptions.debug).toBe(true)
      
      process.env.NODE_ENV = 'production'
      expect(authOptions.debug).toBe(false)
      
      process.env.NODE_ENV = originalEnv
    })
  })
})
