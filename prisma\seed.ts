import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seed...')

  // Create demo users
  const hashedPassword = await bcrypt.hash('demo123', 12)

  const demoUser1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'demo_trader',
      name: 'Demo Trader',
      bio: 'Professional day trader with 5+ years of experience in stocks and crypto.',
      password: hashedPassword,
      verified: true,
      isPublic: true,
      allowCopying: true,
    },
  })

  const demoUser2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'sarah_crypto',
      name: '<PERSON>',
      bio: 'Crypto enthusiast and swing trader. Focus on DeFi and altcoins.',
      password: hashedPassword,
      verified: true,
      isPublic: true,
      allowCopying: true,
    },
  })

  const demoUser3 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'mike_stocks',
      name: 'Mike Chen',
      bio: 'Value investor specializing in tech stocks and options trading.',
      password: hashedPassword,
      verified: true,
      isPublic: true,
      allowCopying: true,
    },
  })

  console.log('✅ Created demo users')

  // Create portfolios
  const portfolio1 = await prisma.portfolio.create({
    data: {
      userId: demoUser1.id,
      name: 'Main Portfolio',
      description: 'Primary trading portfolio for stocks and crypto',
      isDefault: true,
      isPublic: true,
    },
  })

  const portfolio2 = await prisma.portfolio.create({
    data: {
      userId: demoUser2.id,
      name: 'Crypto Focus',
      description: 'Cryptocurrency trading portfolio',
      isDefault: true,
      isPublic: true,
    },
  })

  const portfolio3 = await prisma.portfolio.create({
    data: {
      userId: demoUser3.id,
      name: 'Tech Stocks',
      description: 'Technology sector investments',
      isDefault: true,
      isPublic: true,
    },
  })

  console.log('✅ Created portfolios')

  // Create sample trades
  const sampleTrades = [
    // Demo User 1 trades
    {
      userId: demoUser1.id,
      portfolioId: portfolio1.id,
      symbol: 'AAPL',
      assetType: 'STOCK',
      side: 'BUY',
      quantity: 100,
      entryPrice: 180.50,
      exitPrice: 185.20,
      currentPrice: 185.20,
      status: 'CLOSED',
      entryDate: new Date('2024-01-15T09:30:00Z'),
      exitDate: new Date('2024-01-18T15:45:00Z'),
      realizedPnl: 470.00,
      notes: 'Strong earnings report expected',
      tags: JSON.stringify(['tech', 'earnings']),
      isPublic: true,
    },
    {
      userId: demoUser1.id,
      portfolioId: portfolio1.id,
      symbol: 'TSLA',
      assetType: 'STOCK',
      side: 'BUY',
      quantity: 50,
      entryPrice: 245.80,
      currentPrice: 248.50,
      status: 'OPEN',
      entryDate: new Date('2024-01-20T10:15:00Z'),
      unrealizedPnl: 135.00,
      notes: 'Bullish on EV market growth',
      tags: JSON.stringify(['ev', 'growth']),
      isPublic: true,
    },
    // Demo User 2 trades (crypto focus)
    {
      userId: demoUser2.id,
      portfolioId: portfolio2.id,
      symbol: 'BTC',
      assetType: 'CRYPTO',
      side: 'BUY',
      quantity: 0.5,
      entryPrice: 42000.00,
      exitPrice: 45200.00,
      currentPrice: 45200.00,
      status: 'CLOSED',
      entryDate: new Date('2024-01-10T14:20:00Z'),
      exitDate: new Date('2024-01-22T11:30:00Z'),
      realizedPnl: 1600.00,
      notes: 'Bitcoin breaking resistance levels',
      tags: JSON.stringify(['btc', 'breakout']),
      isPublic: true,
    },
    {
      userId: demoUser2.id,
      portfolioId: portfolio2.id,
      symbol: 'ETH',
      assetType: 'CRYPTO',
      side: 'BUY',
      quantity: 5,
      entryPrice: 2580.00,
      currentPrice: 2650.00,
      status: 'OPEN',
      entryDate: new Date('2024-01-25T16:45:00Z'),
      unrealizedPnl: 350.00,
      notes: 'Ethereum 2.0 upgrade momentum',
      tags: JSON.stringify(['eth', 'upgrade']),
      isPublic: true,
    },
    // Demo User 3 trades (tech stocks)
    {
      userId: demoUser3.id,
      portfolioId: portfolio3.id,
      symbol: 'NVDA',
      assetType: 'STOCK',
      side: 'BUY',
      quantity: 25,
      entryPrice: 720.00,
      currentPrice: 735.50,
      status: 'OPEN',
      entryDate: new Date('2024-01-23T09:45:00Z'),
      unrealizedPnl: 387.50,
      notes: 'AI boom driving semiconductor demand',
      tags: JSON.stringify(['ai', 'semiconductors']),
      isPublic: true,
    },
    {
      userId: demoUser3.id,
      portfolioId: portfolio3.id,
      symbol: 'MSFT',
      assetType: 'STOCK',
      side: 'BUY',
      quantity: 75,
      entryPrice: 375.20,
      exitPrice: 382.10,
      currentPrice: 382.10,
      status: 'CLOSED',
      entryDate: new Date('2024-01-12T11:20:00Z'),
      exitDate: new Date('2024-01-19T14:15:00Z'),
      realizedPnl: 517.50,
      notes: 'Cloud services growth acceleration',
      tags: JSON.stringify(['cloud', 'enterprise']),
      isPublic: true,
    },
  ]

  for (const tradeData of sampleTrades) {
    const trade = await prisma.trade.create({
      data: tradeData as any,
    })

    // Create trade events
    await prisma.tradeEvent.create({
      data: {
        tradeId: trade.id,
        type: 'OPEN',
        quantity: tradeData.quantity,
        price: tradeData.entryPrice,
        notes: tradeData.notes,
      },
    })

    if (tradeData.status === 'CLOSED' && tradeData.exitPrice) {
      await prisma.tradeEvent.create({
        data: {
          tradeId: trade.id,
          type: 'CLOSE',
          quantity: tradeData.quantity,
          price: tradeData.exitPrice,
          notes: 'Position closed',
        },
      })
    }
  }

  console.log('✅ Created sample trades')

  // Create follow relationships
  await prisma.follow.upsert({
    where: {
      followerId_followingId: {
        followerId: demoUser2.id,
        followingId: demoUser1.id,
      }
    },
    update: {},
    create: {
      followerId: demoUser2.id,
      followingId: demoUser1.id,
    },
  })

  await prisma.follow.upsert({
    where: {
      followerId_followingId: {
        followerId: demoUser3.id,
        followingId: demoUser1.id,
      }
    },
    update: {},
    create: {
      followerId: demoUser3.id,
      followingId: demoUser1.id,
    },
  })

  await prisma.follow.upsert({
    where: {
      followerId_followingId: {
        followerId: demoUser3.id,
        followingId: demoUser2.id,
      }
    },
    update: {},
    create: {
      followerId: demoUser3.id,
      followingId: demoUser2.id,
    },
  })

  console.log('✅ Created follow relationships')

  // Create sample notifications
  await prisma.notification.create({
    data: {
      userId: demoUser1.id,
      type: 'NEW_FOLLOWER',
      title: 'New Follower',
      message: 'Sarah Johnson started following you',
      data: JSON.stringify({ followerId: demoUser2.id }),
    },
  })

  await prisma.notification.create({
    data: {
      userId: demoUser1.id,
      type: 'TRADE_COPIED',
      title: 'Trade Copied',
      message: 'Mike Chen copied your AAPL trade',
      data: JSON.stringify({ copyerId: demoUser3.id, symbol: 'AAPL' }),
    },
  })

  console.log('✅ Created sample notifications')

  console.log('🎉 Database seeded successfully!')
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error('❌ Seeding failed:', e)
    await prisma.$disconnect()
    process.exit(1)
  })
