import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

// GET /api/analytics - Get user analytics and performance metrics
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const timeframe = searchParams.get('timeframe') || '30d' // 7d, 30d, 90d, 1y, all
    const userId = session.user.id

    // Calculate date range
    let startDate: Date | undefined
    const now = new Date()
    
    switch (timeframe) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      case '1y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
        break
      default:
        startDate = undefined // All time
    }

    const dateFilter = startDate ? { gte: startDate } : undefined

    // Get basic trade statistics
    const [
      totalTrades,
      openTrades,
      closedTrades,
      totalPnL,
      winningTrades,
      losingTrades,
      avgHoldingTime,
      assetTypeBreakdown,
      monthlyPerformance
    ] = await Promise.all([
      // Total trades count
      prisma.trade.count({
        where: {
          userId,
          ...(startDate && { createdAt: dateFilter })
        }
      }),

      // Open trades count
      prisma.trade.count({
        where: {
          userId,
          status: 'OPEN',
          ...(startDate && { createdAt: dateFilter })
        }
      }),

      // Closed trades count
      prisma.trade.count({
        where: {
          userId,
          status: 'CLOSED',
          ...(startDate && { exitDate: dateFilter })
        }
      }),

      // Total P&L
      prisma.trade.aggregate({
        where: {
          userId,
          status: 'CLOSED',
          realizedPnl: { not: null },
          ...(startDate && { exitDate: dateFilter })
        },
        _sum: {
          realizedPnl: true
        }
      }),

      // Winning trades
      prisma.trade.count({
        where: {
          userId,
          status: 'CLOSED',
          realizedPnl: { gt: 0 },
          ...(startDate && { exitDate: dateFilter })
        }
      }),

      // Losing trades
      prisma.trade.count({
        where: {
          userId,
          status: 'CLOSED',
          realizedPnl: { lt: 0 },
          ...(startDate && { exitDate: dateFilter })
        }
      }),

      // Average holding time (in days)
      prisma.$queryRaw<[{ avgDays: number }]>`
        SELECT AVG(EXTRACT(EPOCH FROM ("exitDate" - "entryDate")) / 86400) as "avgDays"
        FROM "trades"
        WHERE "userId" = ${userId}
        AND "status" = 'CLOSED'
        AND "exitDate" IS NOT NULL
        ${startDate ? `AND "exitDate" >= ${startDate.toISOString()}` : ''}
      `,

      // Asset type breakdown
      prisma.trade.groupBy({
        by: ['assetType'],
        where: {
          userId,
          ...(startDate && { createdAt: dateFilter })
        },
        _count: {
          id: true
        },
        _sum: {
          realizedPnl: true
        }
      }),

      // Monthly performance for charts
      prisma.$queryRaw<Array<{
        month: string
        totalPnl: number
        tradeCount: number
        winRate: number
      }>>`
        SELECT 
          TO_CHAR("exitDate", 'YYYY-MM') as month,
          COALESCE(SUM("realizedPnl"), 0) as "totalPnl",
          COUNT(*) as "tradeCount",
          CASE 
            WHEN COUNT(*) = 0 THEN 0
            ELSE (COUNT(CASE WHEN "realizedPnl" > 0 THEN 1 END) * 100.0 / COUNT(*))
          END as "winRate"
        FROM "trades"
        WHERE "userId" = ${userId}
        AND "status" = 'CLOSED'
        AND "exitDate" IS NOT NULL
        ${startDate ? `AND "exitDate" >= ${startDate.toISOString()}` : ''}
        GROUP BY TO_CHAR("exitDate", 'YYYY-MM')
        ORDER BY month DESC
        LIMIT 12
      `
    ])

    // Calculate derived metrics
    const winRate = closedTrades > 0 ? (winningTrades / closedTrades) * 100 : 0
    const avgWin = winningTrades > 0 ? await getAvgWin(userId, startDate) : 0
    const avgLoss = losingTrades > 0 ? await getAvgLoss(userId, startDate) : 0
    const profitFactor = avgLoss !== 0 ? Math.abs(avgWin / avgLoss) : 0
    const sharpeRatio = await calculateSharpeRatio(userId, startDate)

    // Get top performing assets
    const topAssets = await prisma.trade.groupBy({
      by: ['symbol'],
      where: {
        userId,
        status: 'CLOSED',
        realizedPnl: { not: null },
        ...(startDate && { exitDate: dateFilter })
      },
      _sum: {
        realizedPnl: true
      },
      _count: {
        id: true
      },
      orderBy: {
        _sum: {
          realizedPnl: 'desc'
        }
      },
      take: 10
    })

    const analytics = {
      overview: {
        totalTrades,
        openTrades,
        closedTrades,
        totalPnL: totalPnL._sum.realizedPnl || 0,
        winRate: Math.round(winRate * 100) / 100,
        avgHoldingTime: Math.round((avgHoldingTime[0]?.avgDays || 0) * 100) / 100,
        profitFactor: Math.round(profitFactor * 100) / 100,
        sharpeRatio: Math.round(sharpeRatio * 100) / 100,
      },
      performance: {
        winningTrades,
        losingTrades,
        avgWin: Math.round(avgWin * 100) / 100,
        avgLoss: Math.round(avgLoss * 100) / 100,
        bestTrade: await getBestTrade(userId, startDate),
        worstTrade: await getWorstTrade(userId, startDate),
      },
      breakdown: {
        assetTypes: assetTypeBreakdown.map(item => ({
          assetType: item.assetType,
          count: item._count.id,
          pnl: item._sum.realizedPnl || 0
        })),
        topAssets: topAssets.map(item => ({
          symbol: item.symbol,
          count: item._count.id,
          pnl: item._sum.realizedPnl || 0
        }))
      },
      charts: {
        monthlyPerformance: monthlyPerformance.reverse(),
      },
      timeframe
    }

    return NextResponse.json(analytics)

  } catch (error) {
    console.error('Error fetching analytics:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Helper functions
async function getAvgWin(userId: string, startDate?: Date) {
  const result = await prisma.trade.aggregate({
    where: {
      userId,
      status: 'CLOSED',
      realizedPnl: { gt: 0 },
      ...(startDate && { exitDate: { gte: startDate } })
    },
    _avg: {
      realizedPnl: true
    }
  })
  return result._avg.realizedPnl || 0
}

async function getAvgLoss(userId: string, startDate?: Date) {
  const result = await prisma.trade.aggregate({
    where: {
      userId,
      status: 'CLOSED',
      realizedPnl: { lt: 0 },
      ...(startDate && { exitDate: { gte: startDate } })
    },
    _avg: {
      realizedPnl: true
    }
  })
  return result._avg.realizedPnl || 0
}

async function getBestTrade(userId: string, startDate?: Date) {
  const trade = await prisma.trade.findFirst({
    where: {
      userId,
      status: 'CLOSED',
      realizedPnl: { not: null },
      ...(startDate && { exitDate: { gte: startDate } })
    },
    orderBy: {
      realizedPnl: 'desc'
    },
    select: {
      symbol: true,
      realizedPnl: true,
      exitDate: true
    }
  })
  return trade
}

async function getWorstTrade(userId: string, startDate?: Date) {
  const trade = await prisma.trade.findFirst({
    where: {
      userId,
      status: 'CLOSED',
      realizedPnl: { not: null },
      ...(startDate && { exitDate: { gte: startDate } })
    },
    orderBy: {
      realizedPnl: 'asc'
    },
    select: {
      symbol: true,
      realizedPnl: true,
      exitDate: true
    }
  })
  return trade
}

async function calculateSharpeRatio(userId: string, startDate?: Date): Promise<number> {
  // Simplified Sharpe ratio calculation
  // In a real implementation, you'd want to use daily returns and risk-free rate
  const trades = await prisma.trade.findMany({
    where: {
      userId,
      status: 'CLOSED',
      realizedPnl: { not: null },
      ...(startDate && { exitDate: { gte: startDate } })
    },
    select: {
      realizedPnl: true
    }
  })

  if (trades.length < 2) return 0

  const returns = trades.map(t => Number(t.realizedPnl))
  const avgReturn = returns.reduce((a, b) => a + b, 0) / returns.length
  const variance = returns.reduce((acc, ret) => acc + Math.pow(ret - avgReturn, 2), 0) / returns.length
  const stdDev = Math.sqrt(variance)

  return stdDev !== 0 ? avgReturn / stdDev : 0
}
