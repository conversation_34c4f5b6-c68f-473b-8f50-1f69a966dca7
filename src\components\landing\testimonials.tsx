'use client'

import { motion } from 'framer-motion'
import { Star, Quote } from 'lucide-react'
import Image from 'next/image'

const testimonials = [
  {
    id: 1,
    name: '<PERSON>',
    role: 'Day Trader',
    location: 'San Francisco, CA',
    avatar: '/avatars/sarah.jpg',
    rating: 5,
    content: "TradeFlow Pro has completely transformed my trading. The social features help me learn from other successful traders, and the analytics give me insights I never had before. My win rate has improved by 23% since joining!",
    stats: {
      winRate: '89%',
      totalGain: '+$47,250',
      trades: '342'
    }
  },
  {
    id: 2,
    name: '<PERSON>',
    role: 'Crypto Investor',
    location: 'Miami, FL',
    avatar: '/avatars/marcus.jpg',
    rating: 5,
    content: "The copy trading feature is incredible. I can automatically follow top performers while still maintaining control over my risk management. It's like having a team of expert traders working for me 24/7.",
    stats: {
      winRate: '76%',
      totalGain: '+$23,890',
      trades: '156'
    }
  },
  {
    id: 3,
    name: '<PERSON>',
    role: 'Options Trader',
    location: 'London, UK',
    avatar: '/avatars/emily.jpg',
    rating: 5,
    content: "As someone new to options trading, the educational aspect of following experienced traders has been invaluable. The platform's analytics help me understand not just what to trade, but why and when.",
    stats: {
      winRate: '82%',
      totalGain: '+£18,450',
      trades: '89'
    }
  },
  {
    id: 4,
    name: 'David Kim',
    role: 'Swing Trader',
    location: 'Seoul, South Korea',
    avatar: '/avatars/david.jpg',
    rating: 5,
    content: "The real-time notifications and market data integration are game-changers. I never miss important market movements, and the community insights help me make better decisions faster.",
    stats: {
      winRate: '91%',
      totalGain: '+₩52,340,000',
      trades: '234'
    }
  },
  {
    id: 5,
    name: 'Lisa Thompson',
    role: 'Portfolio Manager',
    location: 'Toronto, Canada',
    avatar: '/avatars/lisa.jpg',
    rating: 5,
    content: "Managing multiple portfolios has never been easier. The advanced analytics and risk management tools help me optimize performance across all my strategies. Highly recommended for serious traders.",
    stats: {
      winRate: '85%',
      totalGain: '+C$89,750',
      trades: '567'
    }
  },
  {
    id: 6,
    name: 'Alex Johnson',
    role: 'Forex Trader',
    location: 'Sydney, Australia',
    avatar: '/avatars/alex.jpg',
    rating: 5,
    content: "The multi-asset support is fantastic. I can track my forex, stocks, and crypto all in one place. The social aspect adds a new dimension to trading that I didn't know I was missing.",
    stats: {
      winRate: '78%',
      totalGain: '+A$31,200',
      trades: '445'
    }
  }
]

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
    },
  },
}

export function Testimonials() {
  return (
    <section className="py-24 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-4">
            What Our{' '}
            <span className="bg-gradient-to-r from-primary to-profit bg-clip-text text-transparent">
              Traders Say
            </span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
            Join thousands of successful traders who have transformed their trading 
            with TradeFlow Pro's advanced tools and social features.
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.id}
              variants={itemVariants}
              className="group relative"
            >
              <div className="relative p-6 bg-card border rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 hover:border-primary/20 h-full flex flex-col">
                {/* Quote icon */}
                <Quote className="h-8 w-8 text-primary/20 mb-4" />
                
                {/* Rating */}
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                  ))}
                </div>

                {/* Content */}
                <blockquote className="text-muted-foreground mb-6 flex-grow">
                  "{testimonial.content}"
                </blockquote>

                {/* Stats */}
                <div className="grid grid-cols-3 gap-4 mb-6 p-4 bg-muted/50 rounded-lg">
                  <div className="text-center">
                    <div className="text-sm font-bold text-profit">{testimonial.stats.winRate}</div>
                    <div className="text-xs text-muted-foreground">Win Rate</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm font-bold text-primary">{testimonial.stats.totalGain}</div>
                    <div className="text-xs text-muted-foreground">Total Gain</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm font-bold text-foreground">{testimonial.stats.trades}</div>
                    <div className="text-xs text-muted-foreground">Trades</div>
                  </div>
                </div>

                {/* Author */}
                <div className="flex items-center">
                  <div className="relative w-12 h-12 rounded-full bg-gradient-to-br from-primary/20 to-profit/20 flex items-center justify-center mr-4">
                    {/* Placeholder for avatar */}
                    <span className="text-lg font-bold text-primary">
                      {testimonial.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <div>
                    <div className="font-semibold text-foreground">{testimonial.name}</div>
                    <div className="text-sm text-muted-foreground">{testimonial.role}</div>
                    <div className="text-xs text-muted-foreground">{testimonial.location}</div>
                  </div>
                </div>

                {/* Hover effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-profit/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="text-center mt-16"
        >
          <div className="inline-flex items-center space-x-4 bg-card border rounded-full px-6 py-3">
            <div className="flex -space-x-2">
              {[1, 2, 3, 4, 5].map((i) => (
                <div
                  key={i}
                  className="w-8 h-8 rounded-full bg-gradient-to-br from-primary/20 to-profit/20 border-2 border-background flex items-center justify-center"
                >
                  <span className="text-xs font-bold text-primary">T</span>
                </div>
              ))}
            </div>
            <div className="text-sm text-muted-foreground">
              <span className="font-semibold text-foreground">4.9/5</span> from 2,500+ reviews
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
