'use client'

import * as React from 'react'
import * as TooltipPrimitive from '@radix-ui/react-tooltip'
import { cn } from '@/lib/utils'

const TooltipProvider = TooltipPrimitive.Provider

const Tooltip = TooltipPrimitive.Root

const TooltipTrigger = TooltipPrimitive.Trigger

const TooltipContent = React.forwardRef<
  React.ElementRef<typeof TooltipPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>
>(({ className, sideOffset = 4, ...props }, ref) => (
  <TooltipPrimitive.Content
    ref={ref}
    sideOffset={sideOffset}
    className={cn(
      'z-50 overflow-hidden rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',
      className
    )}
    {...props}
  />
))
TooltipContent.displayName = TooltipPrimitive.Content.displayName

// Enhanced tooltip with different variants
const tooltipVariants = {
  default: 'bg-primary text-primary-foreground',
  secondary: 'bg-secondary text-secondary-foreground border',
  dark: 'bg-gray-900 text-white',
  light: 'bg-white text-gray-900 border shadow-md',
  success: 'bg-profit text-white',
  error: 'bg-loss text-white',
  warning: 'bg-yellow-500 text-white',
  info: 'bg-blue-500 text-white',
}

interface EnhancedTooltipProps {
  children: React.ReactNode
  content: React.ReactNode
  variant?: keyof typeof tooltipVariants
  side?: 'top' | 'right' | 'bottom' | 'left'
  align?: 'start' | 'center' | 'end'
  delayDuration?: number
  disabled?: boolean
  className?: string
}

function EnhancedTooltip({
  children,
  content,
  variant = 'default',
  side = 'top',
  align = 'center',
  delayDuration = 200,
  disabled = false,
  className,
}: EnhancedTooltipProps) {
  if (disabled) {
    return <>{children}</>
  }

  return (
    <Tooltip delayDuration={delayDuration}>
      <TooltipTrigger asChild>
        {children}
      </TooltipTrigger>
      <TooltipContent
        side={side}
        align={align}
        className={cn(tooltipVariants[variant], className)}
      >
        {content}
      </TooltipContent>
    </Tooltip>
  )
}

// Trading-specific tooltip components
interface TradingTooltipProps {
  children: React.ReactNode
  symbol: string
  price: number
  change: number
  changePercent: number
}

function TradingTooltip({
  children,
  symbol,
  price,
  change,
  changePercent,
}: TradingTooltipProps) {
  const isPositive = change >= 0

  return (
    <EnhancedTooltip
      content={
        <div className="space-y-1">
          <div className="font-semibold">{symbol}</div>
          <div className="text-sm">
            <div>${price.toFixed(2)}</div>
            <div className={isPositive ? 'text-green-300' : 'text-red-300'}>
              {isPositive ? '+' : ''}{change.toFixed(2)} ({changePercent.toFixed(2)}%)
            </div>
          </div>
        </div>
      }
      variant="dark"
      className="min-w-[120px]"
    >
      {children}
    </EnhancedTooltip>
  )
}

interface PnLTooltipProps {
  children: React.ReactNode
  pnl: number
  pnlPercent: number
  entryPrice: number
  currentPrice: number
  quantity: number
}

function PnLTooltip({
  children,
  pnl,
  pnlPercent,
  entryPrice,
  currentPrice,
  quantity,
}: PnLTooltipProps) {
  const isProfit = pnl >= 0

  return (
    <EnhancedTooltip
      content={
        <div className="space-y-2 text-xs">
          <div className="font-semibold">P&L Details</div>
          <div className="space-y-1">
            <div className="flex justify-between">
              <span>Entry Price:</span>
              <span>${entryPrice.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span>Current Price:</span>
              <span>${currentPrice.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span>Quantity:</span>
              <span>{quantity}</span>
            </div>
            <hr className="border-gray-600" />
            <div className="flex justify-between font-semibold">
              <span>Total P&L:</span>
              <span className={isProfit ? 'text-green-300' : 'text-red-300'}>
                ${pnl.toFixed(2)} ({pnlPercent.toFixed(2)}%)
              </span>
            </div>
          </div>
        </div>
      }
      variant="dark"
      className="min-w-[180px]"
    >
      {children}
    </EnhancedTooltip>
  )
}

interface UserTooltipProps {
  children: React.ReactNode
  user: {
    name: string
    username: string
    winRate?: number
    totalTrades?: number
    followers?: number
  }
}

function UserTooltip({ children, user }: UserTooltipProps) {
  return (
    <EnhancedTooltip
      content={
        <div className="space-y-2 text-xs">
          <div>
            <div className="font-semibold">{user.name}</div>
            <div className="text-gray-300">@{user.username}</div>
          </div>
          {(user.winRate || user.totalTrades || user.followers) && (
            <div className="space-y-1">
              {user.winRate && (
                <div className="flex justify-between">
                  <span>Win Rate:</span>
                  <span className="text-green-300">{user.winRate}%</span>
                </div>
              )}
              {user.totalTrades && (
                <div className="flex justify-between">
                  <span>Total Trades:</span>
                  <span>{user.totalTrades}</span>
                </div>
              )}
              {user.followers && (
                <div className="flex justify-between">
                  <span>Followers:</span>
                  <span>{user.followers}</span>
                </div>
              )}
            </div>
          )}
        </div>
      }
      variant="dark"
      className="min-w-[160px]"
    >
      {children}
    </EnhancedTooltip>
  )
}

// Quick tooltip for simple text
function QuickTooltip({
  children,
  text,
  variant = 'default',
}: {
  children: React.ReactNode
  text: string
  variant?: keyof typeof tooltipVariants
}) {
  return (
    <EnhancedTooltip content={text} variant={variant}>
      {children}
    </EnhancedTooltip>
  )
}

// Info tooltip with icon
function InfoTooltip({
  children,
  title,
  description,
}: {
  children: React.ReactNode
  title: string
  description: string
}) {
  return (
    <EnhancedTooltip
      content={
        <div className="space-y-1 max-w-xs">
          <div className="font-semibold text-sm">{title}</div>
          <div className="text-xs text-gray-300">{description}</div>
        </div>
      }
      variant="dark"
      className="max-w-xs"
    >
      {children}
    </EnhancedTooltip>
  )
}

export {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
  TooltipProvider,
  EnhancedTooltip,
  TradingTooltip,
  PnLTooltip,
  UserTooltip,
  QuickTooltip,
  InfoTooltip,
}
