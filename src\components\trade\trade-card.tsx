'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  TrendingUp, 
  TrendingDown, 
  Heart, 
  MessageCircle, 
  Copy, 
  Share2,
  MoreHorizontal,
  Calendar,
  DollarSign,
  Target,
  Clock
} from 'lucide-react'
import { formatCurrency, formatPercentage, formatRelativeTime, getPnLColor, cn } from '@/lib/utils'
import { TradingTooltip, PnLTooltip } from '@/components/ui/tooltip'

interface Trade {
  id: string
  symbol: string
  assetType: string
  side: 'BUY' | 'SELL' | 'SHORT'
  quantity: number
  entryPrice: number
  currentPrice?: number
  exitPrice?: number
  status: 'OPEN' | 'CLOSED' | 'PARTIAL'
  entryDate: string
  exitDate?: string
  realizedPnl?: number
  unrealizedPnl?: number
  notes?: string
  tags: string[]
  isPublic: boolean
  user: {
    id: string
    name: string
    username: string
    avatar?: string
  }
  _count: {
    likes: number
    comments: number
    copiedTrades: number
  }
  isLiked?: boolean
  isCopied?: boolean
}

interface TradeCardProps {
  trade: Trade
  onLike?: (tradeId: string) => void
  onComment?: (tradeId: string) => void
  onCopy?: (tradeId: string) => void
  onShare?: (tradeId: string) => void
  showUser?: boolean
  compact?: boolean
}

export function TradeCard({ 
  trade, 
  onLike, 
  onComment, 
  onCopy, 
  onShare,
  showUser = true,
  compact = false
}: TradeCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const currentPrice = trade.currentPrice || trade.exitPrice || trade.entryPrice
  const pnl = trade.status === 'CLOSED' ? trade.realizedPnl : trade.unrealizedPnl
  const pnlPercent = pnl ? (pnl / (trade.entryPrice * trade.quantity)) * 100 : 0
  
  const sideIcon = trade.side === 'BUY' ? TrendingUp : TrendingDown
  const sideColor = trade.side === 'BUY' ? 'text-profit' : trade.side === 'SELL' ? 'text-loss' : 'text-orange-500'
  
  const statusColor = {
    OPEN: 'bg-blue-500/10 text-blue-500 border-blue-500/20',
    CLOSED: 'bg-gray-500/10 text-gray-500 border-gray-500/20',
    PARTIAL: 'bg-yellow-500/10 text-yellow-500 border-yellow-500/20',
  }[trade.status]

  const SideIcon = sideIcon

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="hover:shadow-md transition-all duration-200 hover:border-primary/20">
        <CardHeader className={cn("pb-3", compact && "pb-2")}>
          <div className="flex items-center justify-between">
            {showUser && (
              <div className="flex items-center space-x-3">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={trade.user.avatar} />
                  <AvatarFallback>
                    {trade.user.name.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className="text-sm font-medium">{trade.user.name}</p>
                  <p className="text-xs text-muted-foreground">@{trade.user.username}</p>
                </div>
              </div>
            )}
            
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className={statusColor}>
                {trade.status}
              </Badge>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className={cn("pt-0", compact && "pb-4")}>
          {/* Trade Header */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <TradingTooltip
                symbol={trade.symbol}
                price={currentPrice}
                change={currentPrice - trade.entryPrice}
                changePercent={((currentPrice - trade.entryPrice) / trade.entryPrice) * 100}
              >
                <div className="flex items-center space-x-2">
                  <div className={cn("p-2 rounded-lg", sideColor.replace('text-', 'bg-') + '/10')}>
                    <SideIcon className={cn("h-4 w-4", sideColor)} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">{trade.symbol}</h3>
                    <p className="text-xs text-muted-foreground">{trade.assetType}</p>
                  </div>
                </div>
              </TradingTooltip>
            </div>

            {pnl !== undefined && (
              <PnLTooltip
                pnl={pnl}
                pnlPercent={pnlPercent}
                entryPrice={trade.entryPrice}
                currentPrice={currentPrice}
                quantity={trade.quantity}
              >
                <div className="text-right">
                  <p className={cn("font-semibold", getPnLColor(pnl))}>
                    {formatCurrency(pnl)}
                  </p>
                  <p className={cn("text-sm", getPnLColor(pnl))}>
                    {formatPercentage(pnlPercent)}
                  </p>
                </div>
              </PnLTooltip>
            )}
          </div>

          {/* Trade Details Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div className="space-y-1">
              <p className="text-xs text-muted-foreground flex items-center">
                <Target className="h-3 w-3 mr-1" />
                Side
              </p>
              <p className={cn("text-sm font-medium", sideColor)}>{trade.side}</p>
            </div>
            
            <div className="space-y-1">
              <p className="text-xs text-muted-foreground flex items-center">
                <DollarSign className="h-3 w-3 mr-1" />
                Quantity
              </p>
              <p className="text-sm font-medium">{trade.quantity}</p>
            </div>
            
            <div className="space-y-1">
              <p className="text-xs text-muted-foreground flex items-center">
                <DollarSign className="h-3 w-3 mr-1" />
                Entry Price
              </p>
              <p className="text-sm font-medium">{formatCurrency(trade.entryPrice)}</p>
            </div>
            
            <div className="space-y-1">
              <p className="text-xs text-muted-foreground flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                {trade.status === 'CLOSED' ? 'Duration' : 'Opened'}
              </p>
              <p className="text-sm font-medium">
                {trade.status === 'CLOSED' && trade.exitDate
                  ? `${Math.ceil((new Date(trade.exitDate).getTime() - new Date(trade.entryDate).getTime()) / (1000 * 60 * 60 * 24))}d`
                  : formatRelativeTime(trade.entryDate)
                }
              </p>
            </div>
          </div>

          {/* Tags */}
          {trade.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-4">
              {trade.tags.slice(0, isExpanded ? undefined : 3).map((tag) => (
                <Badge key={tag} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {trade.tags.length > 3 && !isExpanded && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsExpanded(true)}
                  className="h-6 px-2 text-xs"
                >
                  +{trade.tags.length - 3} more
                </Button>
              )}
            </div>
          )}

          {/* Notes */}
          {trade.notes && (
            <div className="mb-4">
              <p className="text-sm text-muted-foreground">
                {isExpanded ? trade.notes : trade.notes.slice(0, 100)}
                {trade.notes.length > 100 && !isExpanded && (
                  <>
                    ...
                    <button
                      onClick={() => setIsExpanded(true)}
                      className="text-primary hover:underline ml-1"
                    >
                      Read more
                    </button>
                  </>
                )}
              </p>
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center justify-between pt-3 border-t">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onLike?.(trade.id)}
                className={cn(
                  "flex items-center space-x-1",
                  trade.isLiked && "text-red-500"
                )}
              >
                <Heart className={cn("h-4 w-4", trade.isLiked && "fill-current")} />
                <span className="text-xs">{trade._count.likes}</span>
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => onComment?.(trade.id)}
                className="flex items-center space-x-1"
              >
                <MessageCircle className="h-4 w-4" />
                <span className="text-xs">{trade._count.comments}</span>
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => onCopy?.(trade.id)}
                className={cn(
                  "flex items-center space-x-1",
                  trade.isCopied && "text-primary"
                )}
              >
                <Copy className="h-4 w-4" />
                <span className="text-xs">{trade._count.copiedTrades}</span>
              </Button>
            </div>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => onShare?.(trade.id)}
            >
              <Share2 className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
