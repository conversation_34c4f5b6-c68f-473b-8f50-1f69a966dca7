// Global teardown for all tests
const fs = require('fs')
const path = require('path')

module.exports = async () => {
  console.log('🧹 Cleaning up test environment...')
  
  // Clean up test database
  const testDbPath = path.join(process.cwd(), 'prisma', 'test.db')
  if (fs.existsSync(testDbPath)) {
    try {
      fs.unlinkSync(testDbPath)
      console.log('✅ Test database cleaned up')
    } catch (error) {
      console.warn('⚠️ Could not clean up test database:', error.message)
    }
  }
  
  // Clean up any other test artifacts
  const coverageDir = path.join(process.cwd(), 'coverage')
  if (fs.existsSync(coverageDir)) {
    console.log('📊 Coverage reports generated in ./coverage/')
  }
  
  console.log('✅ Test cleanup complete')
}
