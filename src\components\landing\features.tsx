'use client'

import { motion } from 'framer-motion'
import { 
  TrendingUp, 
  Users, 
  BarChart3, 
  Copy, 
  Shield, 
  Zap,
  Globe,
  Bell,
  Target
} from 'lucide-react'

const features = [
  {
    icon: TrendingUp,
    title: 'Advanced Trade Tracking',
    description: 'Track your trades across stocks, crypto, options, and forex with real-time P&L calculations and performance analytics.',
    color: 'text-profit',
    bgColor: 'bg-profit/10',
  },
  {
    icon: Users,
    title: 'Social Trading Network',
    description: 'Follow successful traders, share your strategies, and build a community around your trading expertise.',
    color: 'text-primary',
    bgColor: 'bg-primary/10',
  },
  {
    icon: Copy,
    title: 'Copy Trading',
    description: 'Automatically copy trades from top performers with customizable risk management and position sizing.',
    color: 'text-blue-500',
    bgColor: 'bg-blue-500/10',
  },
  {
    icon: BarChart3,
    title: 'Performance Analytics',
    description: 'Comprehensive analytics with win rates, Sharpe ratios, drawdown analysis, and detailed performance metrics.',
    color: 'text-purple-500',
    bgColor: 'bg-purple-500/10',
  },
  {
    icon: Globe,
    title: 'Multi-Asset Support',
    description: 'Trade and track positions across global markets including stocks, crypto, forex, futures, and options.',
    color: 'text-orange-500',
    bgColor: 'bg-orange-500/10',
  },
  {
    icon: Zap,
    title: 'Real-Time Updates',
    description: 'Get instant notifications and live market data updates to stay ahead of market movements.',
    color: 'text-yellow-500',
    bgColor: 'bg-yellow-500/10',
  },
  {
    icon: Shield,
    title: 'Secure & Private',
    description: 'Bank-level security with encrypted data, secure authentication, and privacy controls for your trading data.',
    color: 'text-green-500',
    bgColor: 'bg-green-500/10',
  },
  {
    icon: Bell,
    title: 'Smart Notifications',
    description: 'Customizable alerts for price movements, trade executions, and social interactions across all your devices.',
    color: 'text-red-500',
    bgColor: 'bg-red-500/10',
  },
  {
    icon: Target,
    title: 'Risk Management',
    description: 'Advanced risk management tools with position sizing, stop losses, and portfolio diversification analysis.',
    color: 'text-indigo-500',
    bgColor: 'bg-indigo-500/10',
  },
]

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
    },
  },
}

export function Features() {
  return (
    <section className="py-24 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-4">
            Everything You Need to{' '}
            <span className="bg-gradient-to-r from-primary to-profit bg-clip-text text-transparent">
              Trade Smarter
            </span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
            Our comprehensive platform combines advanced trading tools with social features 
            to help you make better trading decisions and learn from the best traders.
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              variants={itemVariants}
              className="group relative"
            >
              <div className="relative p-6 bg-card border rounded-xl shadow-sm hover:shadow-md transition-all duration-300 hover:border-primary/20 h-full">
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg ${feature.bgColor} mb-4`}>
                  <feature.icon className={`h-6 w-6 ${feature.color}`} />
                </div>
                
                <h3 className="text-xl font-semibold text-foreground mb-3 group-hover:text-primary transition-colors">
                  {feature.title}
                </h3>
                
                <p className="text-muted-foreground leading-relaxed">
                  {feature.description}
                </p>

                {/* Hover effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-profit/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="text-center mt-16"
        >
          <div className="inline-flex items-center space-x-2 text-sm text-muted-foreground bg-muted/50 px-4 py-2 rounded-full">
            <Zap className="h-4 w-4 text-primary" />
            <span>Join 50,000+ traders already using TradeFlow Pro</span>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
