'use client'

import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useSocket } from '@/lib/socket'
import { useEffect } from 'react'
import axios from 'axios'

// Types
interface Quote {
  symbol: string
  price: number
  change: number
  changePercent: number
  volume: number
  high: number
  low: number
  open: number
  previousClose: number
  timestamp: string
}

interface HistoricalData {
  timestamp: string
  open: number
  high: number
  low: number
  close: number
  volume: number
}

interface MarketDataResponse {
  symbol: string
  data: Quote
  timestamp: string
}

interface BatchQuotesResponse {
  quotes: Array<{
    symbol: string
    assetType: string
    data: Quote
  }>
  timestamp: string
}

interface HistoricalResponse {
  symbol: string
  assetType: string
  period: string
  data: HistoricalData[]
  timestamp: string
}

// Custom hook for single quote
export function useQuote(symbol: string, assetType: 'stock' | 'crypto' = 'stock') {
  const queryClient = useQueryClient()
  const { on, off } = useSocket()

  const query = useQuery({
    queryKey: ['quote', symbol, assetType],
    queryFn: async (): Promise<Quote> => {
      const response = await axios.get(`/api/market?symbol=${symbol}&assetType=${assetType}`)
      return response.data.data
    },
    enabled: !!symbol,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // Refetch every minute
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  })

  // Listen for real-time updates via WebSocket
  useEffect(() => {
    if (!symbol) return

    const handleMarketUpdate = (data: any) => {
      if (data.symbol === symbol.toUpperCase()) {
        queryClient.setQueryData(['quote', symbol, assetType], (old: Quote | undefined) => {
          if (!old) return old
          return {
            ...old,
            price: data.price,
            change: data.change,
            changePercent: data.changePercent,
            timestamp: data.timestamp,
          }
        })
      }
    }

    on('market-update', handleMarketUpdate)
    return () => off('market-update', handleMarketUpdate)
  }, [symbol, assetType, on, off, queryClient])

  return query
}

// Custom hook for batch quotes
export function useBatchQuotes(
  symbols: Array<{ symbol: string; assetType: 'stock' | 'crypto' }>
) {
  const queryClient = useQueryClient()
  const { on, off } = useSocket()

  const query = useQuery({
    queryKey: ['batchQuotes', symbols],
    queryFn: async (): Promise<BatchQuotesResponse> => {
      const symbolsParam = symbols.map(s => s.symbol).join(',')
      const assetTypesParam = symbols.map(s => s.assetType).join(',')
      
      const response = await axios.get(
        `/api/market?symbols=${symbolsParam}&assetTypes=${assetTypesParam}`
      )
      return response.data
    },
    enabled: symbols.length > 0,
    staleTime: 30 * 1000,
    refetchInterval: 60 * 1000,
    retry: 2,
  })

  // Listen for real-time updates
  useEffect(() => {
    if (symbols.length === 0) return

    const handleMarketUpdate = (data: any) => {
      const symbolExists = symbols.some(s => s.symbol.toUpperCase() === data.symbol)
      if (symbolExists) {
        queryClient.setQueryData(['batchQuotes', symbols], (old: BatchQuotesResponse | undefined) => {
          if (!old) return old
          return {
            ...old,
            quotes: old.quotes.map(quote => 
              quote.symbol === data.symbol
                ? {
                    ...quote,
                    data: {
                      ...quote.data,
                      price: data.price,
                      change: data.change,
                      changePercent: data.changePercent,
                      timestamp: data.timestamp,
                    }
                  }
                : quote
            ),
            timestamp: new Date().toISOString(),
          }
        })
      }
    }

    on('market-update', handleMarketUpdate)
    return () => off('market-update', handleMarketUpdate)
  }, [symbols, on, off, queryClient])

  return query
}

// Custom hook for historical data
export function useHistoricalData(
  symbol: string,
  assetType: 'stock' | 'crypto' = 'stock',
  period: '1d' | '7d' | '30d' | '90d' | '1y' = '30d'
) {
  return useQuery({
    queryKey: ['historical', symbol, assetType, period],
    queryFn: async (): Promise<HistoricalData[]> => {
      const response = await axios.get(
        `/api/market/historical?symbol=${symbol}&assetType=${assetType}&period=${period}`
      )
      return response.data.data
    },
    enabled: !!symbol,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

// Custom hook for symbol search
export function useSymbolSearch(query: string) {
  return useQuery({
    queryKey: ['symbolSearch', query],
    queryFn: async () => {
      const response = await axios.get(`/api/market/search?q=${encodeURIComponent(query)}`)
      return response.data.results
    },
    enabled: query.length >= 2,
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 1,
  })
}

// Custom hook for trending symbols
export function useTrendingSymbols() {
  return useQuery({
    queryKey: ['trending'],
    queryFn: async () => {
      const response = await axios.get('/api/market/trending')
      return response.data.trending
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes
    retry: 2,
  })
}

// Custom hook for watchlist quotes
export function useWatchlistQuotes(watchlist: string[]) {
  const symbols = watchlist.map(symbol => ({
    symbol,
    assetType: 'stock' as const, // You might want to store asset type with watchlist
  }))

  return useBatchQuotes(symbols)
}

// Custom hook for portfolio quotes (for open trades)
export function usePortfolioQuotes(trades: Array<{ symbol: string; assetType: string }>) {
  const symbols = trades.map(trade => ({
    symbol: trade.symbol,
    assetType: trade.assetType.toLowerCase() === 'crypto' ? 'crypto' as const : 'stock' as const,
  }))

  return useBatchQuotes(symbols)
}

// Utility hook for real-time price updates
export function useRealTimePrice(symbol: string, assetType: 'stock' | 'crypto' = 'stock') {
  const queryClient = useQueryClient()
  const { on, off, isConnected } = useSocket()

  useEffect(() => {
    if (!symbol || !isConnected) return

    const handlePriceUpdate = (data: any) => {
      if (data.symbol === symbol.toUpperCase()) {
        // Update all relevant queries
        queryClient.setQueryData(['quote', symbol, assetType], (old: Quote | undefined) => {
          if (!old) return old
          return {
            ...old,
            price: data.price,
            change: data.change,
            changePercent: data.changePercent,
            timestamp: data.timestamp,
          }
        })

        // Also update batch quotes if this symbol is included
        queryClient.setQueriesData(
          { queryKey: ['batchQuotes'] },
          (old: BatchQuotesResponse | undefined) => {
            if (!old) return old
            return {
              ...old,
              quotes: old.quotes.map(quote => 
                quote.symbol === data.symbol
                  ? {
                      ...quote,
                      data: {
                        ...quote.data,
                        price: data.price,
                        change: data.change,
                        changePercent: data.changePercent,
                        timestamp: data.timestamp,
                      }
                    }
                  : quote
              ),
            }
          }
        )
      }
    }

    on('market-update', handlePriceUpdate)
    return () => off('market-update', handlePriceUpdate)
  }, [symbol, assetType, isConnected, on, off, queryClient])
}

// Hook for prefetching market data
export function usePrefetchMarketData() {
  const queryClient = useQueryClient()

  const prefetchQuote = (symbol: string, assetType: 'stock' | 'crypto' = 'stock') => {
    queryClient.prefetchQuery({
      queryKey: ['quote', symbol, assetType],
      queryFn: async () => {
        const response = await axios.get(`/api/market?symbol=${symbol}&assetType=${assetType}`)
        return response.data.data
      },
      staleTime: 30 * 1000,
    })
  }

  const prefetchHistorical = (
    symbol: string,
    assetType: 'stock' | 'crypto' = 'stock',
    period: '1d' | '7d' | '30d' | '90d' | '1y' = '30d'
  ) => {
    queryClient.prefetchQuery({
      queryKey: ['historical', symbol, assetType, period],
      queryFn: async () => {
        const response = await axios.get(
          `/api/market/historical?symbol=${symbol}&assetType=${assetType}&period=${period}`
        )
        return response.data.data
      },
      staleTime: 5 * 60 * 1000,
    })
  }

  return { prefetchQuote, prefetchHistorical }
}
