import { NextAuthOptions } from 'next-auth'
import { PrismaAdapter } from '@next-auth/prisma-adapter'
import GoogleProvider from 'next-auth/providers/google'
import GitHubProvider from 'next-auth/providers/github'
import Cred<PERSON><PERSON><PERSON>rovider from 'next-auth/providers/credentials'
import bcrypt from 'bcryptjs'
import { prisma } from '@/lib/db'
import { User } from '@prisma/client'

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    GitHubProvider({
      clientId: process.env.GITHUB_CLIENT_ID!,
      clientSecret: process.env.GITHUB_CLIENT_SECRET!,
    }),
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        const user = await prisma.user.findUnique({
          where: {
            email: credentials.email
          }
        })

        if (!user || !user.password) {
          return null
        }

        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.password
        )

        if (!isPasswordValid) {
          return null
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          username: user.username,
          avatar: user.avatar,
          verified: user.verified,
        }
      }
    })
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  pages: {
    signIn: '/auth/signin',
    signUp: '/auth/signup',
    error: '/auth/error',
  },
  callbacks: {
    async jwt({ token, user, account }) {
      if (user) {
        token.username = (user as any).username
        token.verified = (user as any).verified
      }
      
      // Handle OAuth account linking
      if (account && account.provider !== 'credentials') {
        const existingUser = await prisma.user.findUnique({
          where: { email: token.email! }
        })
        
        if (existingUser) {
          token.id = existingUser.id
          token.username = existingUser.username
          token.verified = existingUser.verified
        }
      }
      
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string
        session.user.username = token.username as string
        session.user.verified = token.verified as boolean
      }
      return session
    },
    async signIn({ user, account, profile }) {
      if (account?.provider === 'google' || account?.provider === 'github') {
        try {
          const existingUser = await prisma.user.findUnique({
            where: { email: user.email! }
          })

          if (!existingUser) {
            // Create username from email or profile
            let username = user.email!.split('@')[0]
            
            // Ensure username is unique
            const existingUsername = await prisma.user.findUnique({
              where: { username }
            })
            
            if (existingUsername) {
              username = `${username}_${Math.random().toString(36).substr(2, 9)}`
            }

            await prisma.user.create({
              data: {
                email: user.email!,
                name: user.name,
                username,
                avatar: user.image,
                emailVerified: new Date(),
              }
            })
          }
        } catch (error) {
          console.error('Error creating user:', error)
          return false
        }
      }
      
      return true
    },
  },
  events: {
    async createUser({ user }) {
      // Send welcome email or perform other actions
      console.log('New user created:', user.email)
    },
    async signIn({ user, account, isNewUser }) {
      console.log('User signed in:', user.email, 'Provider:', account?.provider)
    },
  },
  debug: process.env.NODE_ENV === 'development',
}
