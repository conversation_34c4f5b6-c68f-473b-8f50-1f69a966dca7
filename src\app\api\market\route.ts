import { NextRequest, NextResponse } from 'next/server'
import { marketDataService } from '@/lib/api/market-data'
import { z } from 'zod'

const quoteSchema = z.object({
  symbol: z.string().min(1).max(10),
  assetType: z.enum(['stock', 'crypto']).optional().default('stock'),
})

const batchQuoteSchema = z.object({
  symbols: z.array(z.object({
    symbol: z.string().min(1).max(10),
    assetType: z.enum(['stock', 'crypto']).optional().default('stock'),
  })).min(1).max(50), // Limit to 50 symbols per request
})

const historicalSchema = z.object({
  symbol: z.string().min(1).max(10),
  assetType: z.enum(['stock', 'crypto']).optional().default('stock'),
  period: z.enum(['1d', '7d', '30d', '90d', '1y']).optional().default('30d'),
})

// GET /api/market?symbol=AAPL&assetType=stock - Get single quote
// GET /api/market?symbols=AAPL,TSLA,BTC&assetTypes=stock,stock,crypto - Get multiple quotes
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const symbol = searchParams.get('symbol')
    const symbols = searchParams.get('symbols')
    const assetType = searchParams.get('assetType') || 'stock'
    const assetTypes = searchParams.get('assetTypes')

    // Handle single symbol request
    if (symbol) {
      const validatedData = quoteSchema.parse({ symbol, assetType })
      
      try {
        const quote = await marketDataService.getQuote(
          validatedData.symbol.toUpperCase(),
          validatedData.assetType
        )
        
        return NextResponse.json({
          symbol: validatedData.symbol.toUpperCase(),
          data: quote,
          timestamp: new Date().toISOString()
        })
      } catch (error) {
        return NextResponse.json(
          { 
            error: 'Failed to fetch market data',
            symbol: validatedData.symbol.toUpperCase(),
            message: error instanceof Error ? error.message : 'Unknown error'
          },
          { status: 503 }
        )
      }
    }

    // Handle multiple symbols request
    if (symbols) {
      const symbolArray = symbols.split(',').map(s => s.trim())
      const assetTypeArray = assetTypes ? assetTypes.split(',').map(s => s.trim()) : []
      
      const symbolsWithTypes = symbolArray.map((sym, index) => ({
        symbol: sym,
        assetType: (assetTypeArray[index] || 'stock') as 'stock' | 'crypto'
      }))

      const validatedData = batchQuoteSchema.parse({ symbols: symbolsWithTypes })
      
      try {
        const quotes = await marketDataService.getBatchQuotes(
          validatedData.symbols.map(s => ({
            symbol: s.symbol.toUpperCase(),
            assetType: s.assetType
          }))
        )
        
        return NextResponse.json({
          quotes: quotes.map((quote, index) => ({
            symbol: validatedData.symbols[index].symbol.toUpperCase(),
            assetType: validatedData.symbols[index].assetType,
            data: quote,
          })),
          timestamp: new Date().toISOString()
        })
      } catch (error) {
        return NextResponse.json(
          { 
            error: 'Failed to fetch batch market data',
            message: error instanceof Error ? error.message : 'Unknown error'
          },
          { status: 503 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Symbol or symbols parameter is required' },
      { status: 400 }
    )

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Market data API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET /api/market/historical?symbol=AAPL&assetType=stock&period=30d
export async function getHistoricalData(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const symbol = searchParams.get('symbol')
    const assetType = searchParams.get('assetType') || 'stock'
    const period = searchParams.get('period') || '30d'

    if (!symbol) {
      return NextResponse.json(
        { error: 'Symbol parameter is required' },
        { status: 400 }
      )
    }

    const validatedData = historicalSchema.parse({ symbol, assetType, period })
    
    try {
      const historicalData = await marketDataService.getHistoricalData(
        validatedData.symbol.toUpperCase(),
        validatedData.assetType,
        validatedData.period
      )
      
      return NextResponse.json({
        symbol: validatedData.symbol.toUpperCase(),
        assetType: validatedData.assetType,
        period: validatedData.period,
        data: historicalData,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      return NextResponse.json(
        { 
          error: 'Failed to fetch historical data',
          symbol: validatedData.symbol.toUpperCase(),
          message: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 503 }
      )
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Historical data API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET /api/market/search?q=apple - Search for symbols
export async function searchSymbols(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')

    if (!query || query.length < 2) {
      return NextResponse.json(
        { error: 'Query must be at least 2 characters' },
        { status: 400 }
      )
    }

    // Mock search results - in a real implementation, you'd use a symbol search API
    const mockResults = [
      { symbol: 'AAPL', name: 'Apple Inc.', type: 'stock', exchange: 'NASDAQ' },
      { symbol: 'GOOGL', name: 'Alphabet Inc.', type: 'stock', exchange: 'NASDAQ' },
      { symbol: 'MSFT', name: 'Microsoft Corporation', type: 'stock', exchange: 'NASDAQ' },
      { symbol: 'TSLA', name: 'Tesla, Inc.', type: 'stock', exchange: 'NASDAQ' },
      { symbol: 'BTC', name: 'Bitcoin', type: 'crypto', exchange: 'Crypto' },
      { symbol: 'ETH', name: 'Ethereum', type: 'crypto', exchange: 'Crypto' },
    ].filter(item => 
      item.symbol.toLowerCase().includes(query.toLowerCase()) ||
      item.name.toLowerCase().includes(query.toLowerCase())
    )

    return NextResponse.json({
      query,
      results: mockResults,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Symbol search API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET /api/market/trending - Get trending symbols
export async function getTrendingSymbols(request: NextRequest) {
  try {
    // Mock trending data - in a real implementation, you'd fetch from market data providers
    const trending = {
      stocks: [
        { symbol: 'AAPL', name: 'Apple Inc.', change: 2.34, changePercent: 1.28 },
        { symbol: 'TSLA', name: 'Tesla, Inc.', change: -5.67, changePercent: -2.31 },
        { symbol: 'NVDA', name: 'NVIDIA Corporation', change: 15.23, changePercent: 2.89 },
        { symbol: 'MSFT', name: 'Microsoft Corporation', change: 3.45, changePercent: 0.92 },
        { symbol: 'GOOGL', name: 'Alphabet Inc.', change: -2.11, changePercent: -1.45 },
      ],
      crypto: [
        { symbol: 'BTC', name: 'Bitcoin', change: 1250.50, changePercent: 2.95 },
        { symbol: 'ETH', name: 'Ethereum', change: -45.30, changePercent: -1.67 },
        { symbol: 'ADA', name: 'Cardano', change: 0.025, changePercent: 4.12 },
        { symbol: 'SOL', name: 'Solana', change: 3.45, changePercent: 3.78 },
        { symbol: 'DOT', name: 'Polkadot', change: -0.67, changePercent: -2.34 },
      ]
    }

    return NextResponse.json({
      trending,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Trending symbols API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
