// User types
export interface User {
  id: string
  email: string
  username: string
  name: string
  bio?: string
  avatar?: string
  verified: boolean
  isPublic: boolean
  allowCopying: boolean
  createdAt: string
  updatedAt: string
  _count?: {
    followers: number
    following: number
    trades: number
  }
  stats?: {
    totalPnL: number
    winRate: number
    totalTrades: number
    followers: number
    following: number
  }
  isFollowing?: boolean
}

// Trade types
export interface Trade {
  id: string
  userId: string
  portfolioId?: string
  symbol: string
  assetType: AssetType
  side: TradeSide
  quantity: number
  entryPrice: number
  exitPrice?: number
  currentPrice?: number
  status: TradeStatus
  entryDate: string
  exitDate?: string
  createdAt: string
  updatedAt: string
  realizedPnl?: number
  unrealizedPnl?: number
  notes?: string
  tags: string[]
  isPublic: boolean
  originalTradeId?: string
  user?: User
  portfolio?: Portfolio
  events?: TradeEvent[]
  _count?: {
    likes: number
    comments: number
    copiedTrades: number
  }
  isLiked?: boolean
  isCopied?: boolean
}

export interface TradeEvent {
  id: string
  tradeId: string
  type: TradeEventType
  quantity?: number
  price: number
  notes?: string
  createdAt: string
}

// Portfolio types
export interface Portfolio {
  id: string
  userId: string
  name: string
  description?: string
  isDefault: boolean
  isPublic: boolean
  createdAt: string
  updatedAt: string
  trades?: Trade[]
  _count?: {
    trades: number
  }
  stats?: {
    totalValue: number
    totalPnL: number
    winRate: number
    bestTrade?: Trade
    worstTrade?: Trade
  }
}

// Social types
export interface Follow {
  id: string
  followerId: string
  followingId: string
  createdAt: string
  follower?: User
  following?: User
}

export interface Like {
  id: string
  userId: string
  tradeId: string
  createdAt: string
  user?: User
  trade?: Trade
}

export interface Comment {
  id: string
  userId: string
  tradeId: string
  content: string
  createdAt: string
  updatedAt: string
  user?: User
  trade?: Trade
}

// Notification types
export interface Notification {
  id: string
  userId: string
  type: NotificationType
  title: string
  message: string
  read: boolean
  data?: Record<string, any>
  createdAt: string
  user?: User
}

// Market data types
export interface Quote {
  symbol: string
  price: number
  change: number
  changePercent: number
  volume: number
  marketCap?: number
  high: number
  low: number
  open: number
  previousClose: number
  timestamp: string
}

export interface HistoricalData {
  timestamp: string
  open: number
  high: number
  low: number
  close: number
  volume: number
}

export interface MarketSymbol {
  symbol: string
  name: string
  type: 'stock' | 'crypto' | 'forex' | 'option' | 'future' | 'etf' | 'index'
  exchange?: string
}

// Analytics types
export interface Analytics {
  overview: {
    totalTrades: number
    openTrades: number
    closedTrades: number
    totalPnL: number
    winRate: number
    avgHoldingTime: number
    profitFactor: number
    sharpeRatio: number
  }
  performance: {
    winningTrades: number
    losingTrades: number
    avgWin: number
    avgLoss: number
    bestTrade?: {
      symbol: string
      realizedPnl: number
      exitDate: string
    }
    worstTrade?: {
      symbol: string
      realizedPnl: number
      exitDate: string
    }
  }
  breakdown: {
    assetTypes: Array<{
      assetType: string
      count: number
      pnl: number
    }>
    topAssets: Array<{
      symbol: string
      count: number
      pnl: number
    }>
  }
  charts: {
    monthlyPerformance: Array<{
      month: string
      totalPnl: number
      tradeCount: number
      winRate: number
    }>
  }
  timeframe: string
}

// API Response types
export interface ApiResponse<T = any> {
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// Form types
export interface TradeFormData {
  symbol: string
  assetType: AssetType
  side: TradeSide
  quantity: number
  entryPrice: number
  entryDate: string
  portfolioId?: string
  notes?: string
  tags?: string[]
  isPublic?: boolean
}

export interface UserFormData {
  name: string
  bio?: string
  isPublic?: boolean
  allowCopying?: boolean
}

export interface PortfolioFormData {
  name: string
  description?: string
  isPublic?: boolean
}

// Enums
export enum AssetType {
  STOCK = 'STOCK',
  OPTION = 'OPTION',
  CRYPTO = 'CRYPTO',
  FOREX = 'FOREX',
  FUTURE = 'FUTURE',
  ETF = 'ETF',
  INDEX = 'INDEX',
}

export enum TradeSide {
  BUY = 'BUY',
  SELL = 'SELL',
  SHORT = 'SHORT',
}

export enum TradeStatus {
  OPEN = 'OPEN',
  CLOSED = 'CLOSED',
  PARTIAL = 'PARTIAL',
  CANCELLED = 'CANCELLED',
}

export enum TradeEventType {
  OPEN = 'OPEN',
  PARTIAL_CLOSE = 'PARTIAL_CLOSE',
  CLOSE = 'CLOSE',
  UPDATE = 'UPDATE',
  NOTE = 'NOTE',
}

export enum NotificationType {
  TRADE_COPIED = 'TRADE_COPIED',
  NEW_FOLLOWER = 'NEW_FOLLOWER',
  TRADE_LIKED = 'TRADE_LIKED',
  TRADE_COMMENTED = 'TRADE_COMMENTED',
  SYSTEM = 'SYSTEM',
}

// Socket event types
export interface SocketEvents {
  'trade-update': {
    type: 'trade-created' | 'trade-updated' | 'trade-closed'
    trade: Trade
    userId: string
  }
  'market-update': {
    symbol: string
    price: number
    change: number
    changePercent: number
    timestamp: string
  }
  'notification': Notification
  'follower-update': {
    type: 'new-follower' | 'unfollowed'
    followerId: string
    followingId: string
    followerData?: User
  }
}

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>

// Error types
export interface ApiError {
  error: string
  message?: string
  details?: any
  status?: number
}

export interface ValidationError {
  field: string
  message: string
  code?: string
}

// Filter and sort types
export interface TradeFilters {
  status?: TradeStatus
  assetType?: AssetType
  side?: TradeSide
  portfolioId?: string
  dateFrom?: string
  dateTo?: string
  minPnL?: number
  maxPnL?: number
  tags?: string[]
}

export interface SortOptions {
  field: string
  direction: 'asc' | 'desc'
}

export interface PaginationOptions {
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}
