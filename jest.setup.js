// Jest setup file for global test configuration
import '@testing-library/jest-dom'

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter() {
    return {
      route: '/',
      pathname: '/',
      query: {},
      asPath: '/',
      push: jest.fn(),
      pop: jest.fn(),
      reload: jest.fn(),
      back: jest.fn(),
      prefetch: jest.fn().mockResolvedValue(undefined),
      beforePopState: jest.fn(),
      events: {
        on: jest.fn(),
        off: jest.fn(),
        emit: jest.fn(),
      },
      isFallback: false,
    }
  },
}))

// Mock Next.js navigation (App Router)
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
    }
  },
  useSearchParams() {
    return new URLSearchParams()
  },
  usePathname() {
    return '/'
  },
}))

// Mock NextAuth
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(() => ({
    data: null,
    status: 'unauthenticated',
  })),
  signIn: jest.fn(),
  signOut: jest.fn(),
  getSession: jest.fn(),
  SessionProvider: ({ children }) => children,
}))

// Mock Prisma Client
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    user: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      upsert: jest.fn(),
      count: jest.fn(),
    },
    trade: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    portfolio: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    follow: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      upsert: jest.fn(),
    },
    notification: {
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    tradeEvent: {
      create: jest.fn(),
      findMany: jest.fn(),
    },
    like: {
      create: jest.fn(),
      delete: jest.fn(),
      findUnique: jest.fn(),
    },
    comment: {
      create: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    $connect: jest.fn(),
    $disconnect: jest.fn(),
    $transaction: jest.fn(),
  })),
}))

// Mock Redis/IORedis
jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => ({
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    incr: jest.fn(),
    expire: jest.fn(),
    pipeline: jest.fn(() => ({
      incr: jest.fn(),
      expire: jest.fn(),
      exec: jest.fn().mockResolvedValue([[null, 1], [null, 'OK']]),
    })),
    disconnect: jest.fn(),
  }))
})

// Mock Socket.io
jest.mock('socket.io-client', () => ({
  io: jest.fn(() => ({
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn(),
    connect: jest.fn(),
    disconnect: jest.fn(),
    connected: true,
  })),
}))

// Mock market data service
jest.mock('../src/lib/api/market-data', () => ({
  marketDataService: {
    getQuote: jest.fn().mockResolvedValue({
      symbol: 'AAPL',
      price: 150.00,
      change: 2.50,
      changePercent: 1.69,
      volume: 1000000,
      high: 152.00,
      low: 148.00,
      open: 149.00,
      previousClose: 147.50,
      timestamp: new Date().toISOString(),
    }),
    getBatchQuotes: jest.fn().mockResolvedValue([
      {
        symbol: 'AAPL',
        price: 150.00,
        change: 2.50,
        changePercent: 1.69,
        volume: 1000000,
        high: 152.00,
        low: 148.00,
        open: 149.00,
        previousClose: 147.50,
        timestamp: new Date().toISOString(),
      },
    ]),
    getHistoricalData: jest.fn().mockResolvedValue([]),
  },
}))

// Mock bcryptjs
jest.mock('bcryptjs', () => ({
  hash: jest.fn().mockResolvedValue('hashed_password'),
  compare: jest.fn().mockResolvedValue(true),
  genSalt: jest.fn().mockResolvedValue('salt'),
}))

// Mock jsonwebtoken
jest.mock('jsonwebtoken', () => ({
  sign: jest.fn().mockReturnValue('mock_jwt_token'),
  verify: jest.fn().mockReturnValue({ userId: 'user123', email: '<EMAIL>' }),
  decode: jest.fn().mockReturnValue({ userId: 'user123', email: '<EMAIL>' }),
}))

// Global test utilities
global.mockUser = {
  id: 'user123',
  email: '<EMAIL>',
  username: 'testuser',
  name: 'Test User',
  verified: true,
  isPublic: true,
  allowCopying: true,
  createdAt: new Date(),
  updatedAt: new Date(),
}

global.mockTrade = {
  id: 'trade123',
  userId: 'user123',
  symbol: 'AAPL',
  assetType: 'STOCK',
  side: 'BUY',
  quantity: 100,
  entryPrice: 150.00,
  currentPrice: 152.50,
  status: 'OPEN',
  entryDate: new Date(),
  createdAt: new Date(),
  updatedAt: new Date(),
  isPublic: true,
  unrealizedPnl: 250.00,
}

// Console error suppression for tests
const originalError = console.error
beforeAll(() => {
  console.error = (...args) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Warning: ReactDOM.render is no longer supported')
    ) {
      return
    }
    originalError.call(console, ...args)
  }
})

afterAll(() => {
  console.error = originalError
})

// Increase timeout for all tests
jest.setTimeout(10000)

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks()
})
