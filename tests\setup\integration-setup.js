// Setup for integration tests
import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

// Create a test Prisma client for integration tests
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: 'file:./test.db'
    }
  }
})

global.integrationPrisma = prisma

// Integration test utilities
global.integrationUtils = {
  // Create complete test user with portfolio
  createCompleteTestUser: async (overrides = {}) => {
    const hashedPassword = await bcrypt.hash('testpassword123', 12)
    
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'integrationuser',
        name: 'Integration Test User',
        password: hashedPassword,
        verified: true,
        isPublic: true,
        allowCopying: true,
        ...overrides,
      },
    })
    
    // Create default portfolio
    const portfolio = await prisma.portfolio.create({
      data: {
        userId: user.id,
        name: 'Main Portfolio',
        description: 'Default portfolio for integration tests',
        isDefault: true,
        isPublic: true,
      },
    })
    
    return { user, portfolio }
  },
  
  // Create test trade with events
  createCompleteTestTrade: async (userId, portfolioId, overrides = {}) => {
    const trade = await prisma.trade.create({
      data: {
        userId,
        portfolioId,
        symbol: 'AAPL',
        assetType: 'STOCK',
        side: 'BUY',
        quantity: 100,
        entryPrice: 150.00,
        currentPrice: 152.50,
        status: 'OPEN',
        entryDate: new Date(),
        isPublic: true,
        ...overrides,
      },
    })
    
    // Create trade event
    await prisma.tradeEvent.create({
      data: {
        tradeId: trade.id,
        type: 'OPEN',
        quantity: trade.quantity,
        price: trade.entryPrice,
        notes: 'Trade opened in integration test',
      },
    })
    
    return trade
  },
  
  // Create follow relationship
  createFollowRelationship: async (followerId, followingId) => {
    return await prisma.follow.create({
      data: {
        followerId,
        followingId,
      },
    })
  },
  
  // Create test notification
  createTestNotification: async (userId, overrides = {}) => {
    return await prisma.notification.create({
      data: {
        userId,
        type: 'SYSTEM',
        title: 'Test Notification',
        message: 'This is a test notification',
        read: false,
        ...overrides,
      },
    })
  },
  
  // Seed test data for integration tests
  seedTestData: async () => {
    // Create multiple users
    const users = []
    for (let i = 1; i <= 3; i++) {
      const hashedPassword = await bcrypt.hash('testpassword123', 12)
      const user = await prisma.user.create({
        data: {
          email: `user${i}@example.com`,
          username: `testuser${i}`,
          name: `Test User ${i}`,
          password: hashedPassword,
          verified: true,
          isPublic: true,
          allowCopying: true,
        },
      })
      users.push(user)
      
      // Create portfolio for each user
      await prisma.portfolio.create({
        data: {
          userId: user.id,
          name: `Portfolio ${i}`,
          description: `Test portfolio for user ${i}`,
          isDefault: true,
          isPublic: true,
        },
      })
    }
    
    return users
  },
  
  // Clean up all test data
  cleanupIntegrationData: async () => {
    // Delete in correct order to respect foreign key constraints
    await prisma.tradeEvent.deleteMany()
    await prisma.like.deleteMany()
    await prisma.comment.deleteMany()
    await prisma.notification.deleteMany()
    await prisma.trade.deleteMany()
    await prisma.follow.deleteMany()
    await prisma.portfolio.deleteMany()
    await prisma.account.deleteMany()
    await prisma.session.deleteMany()
    await prisma.user.deleteMany()
  },
}

// Setup and teardown for integration tests
beforeEach(async () => {
  await global.integrationUtils.cleanupIntegrationData()
})

afterAll(async () => {
  await global.integrationUtils.cleanupIntegrationData()
  await prisma.$disconnect()
})
