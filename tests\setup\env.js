// Environment setup for tests
process.env.NODE_ENV = 'test'
process.env.DATABASE_URL = 'file:./test.db'
process.env.NEXTAUTH_URL = 'http://localhost:3000'
process.env.NEXTAUTH_SECRET = 'test-secret-key'
process.env.RATE_LIMIT_ENABLED = 'false'
process.env.REDIS_URL = ''

// Disable external API calls in tests
process.env.ALPHA_VANTAGE_API_KEY = ''
process.env.POLYGON_API_KEY = ''
process.env.COINGECKO_API_KEY = ''

// Feature flags for testing
process.env.ENABLE_COPY_TRADING = 'true'
process.env.ENABLE_PAPER_TRADING = 'true'
process.env.ENABLE_SOCIAL_SHARING = 'true'
process.env.ENABLE_REAL_TIME_UPDATES = 'false'
