// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id @default(cuid())
  email         String    @unique
  username      String    @unique
  name          String?
  bio           String?
  avatar        String?
  verified      <PERSON><PERSON><PERSON>   @default(false)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  // Authentication
  password      String?
  emailVerified DateTime?
  
  // Profile settings
  isPublic      Boolean   @default(true)
  allowCopying  <PERSON>olean   @default(true)
  
  // Social
  followers     Follow[]  @relation("UserFollowers")
  following     Follow[]  @relation("UserFollowing")
  
  // Trading
  trades        Trade[]
  portfolios    Portfolio[]
  
  // Social interactions
  likes         Like[]
  comments      Comment[]
  
  // Notifications
  notifications Notification[]
  
  // OAuth accounts
  accounts      Account[]
  sessions      Session[]
  
  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model Follow {
  id          String   @id @default(cuid())
  followerId  String
  followingId String
  createdAt   DateTime @default(now())
  
  follower    User @relation("UserFollowing", fields: [followerId], references: [id], onDelete: Cascade)
  following   User @relation("UserFollowers", fields: [followingId], references: [id], onDelete: Cascade)
  
  @@unique([followerId, followingId])
  @@map("follows")
}

model Portfolio {
  id          String   @id @default(cuid())
  userId      String
  name        String
  description String?
  isDefault   Boolean  @default(false)
  isPublic    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  user        User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  trades      Trade[]
  
  @@map("portfolios")
}

model Trade {
  id           String      @id @default(cuid())
  userId       String
  portfolioId  String?
  
  // Trade details
  symbol       String
  assetType    String           // STOCK, OPTION, CRYPTO, FOREX, FUTURE, ETF, INDEX
  side         String           // BUY, SELL, SHORT
  quantity     Decimal
  entryPrice   Decimal
  exitPrice    Decimal?
  currentPrice Decimal?
  
  // Trade status
  status       String      @default("OPEN") // OPEN, CLOSED, PARTIAL, CANCELLED
  
  // Timestamps
  entryDate    DateTime
  exitDate     DateTime?
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  
  // P&L calculations
  realizedPnl  Decimal?
  unrealizedPnl Decimal?
  
  // Additional info
  notes        String?
  tags         String?          // JSON string for SQLite compatibility
  
  // Social features
  isPublic     Boolean     @default(true)
  likes        Like[]
  comments     Comment[]
  
  // Copy trading
  originalTradeId String?
  originalTrade   Trade?  @relation("CopiedTrades", fields: [originalTradeId], references: [id])
  copiedTrades    Trade[] @relation("CopiedTrades")
  
  // Relations
  user         User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  portfolio    Portfolio?  @relation(fields: [portfolioId], references: [id])
  events       TradeEvent[]
  
  @@map("trades")
}

model TradeEvent {
  id        String          @id @default(cuid())
  tradeId   String
  type      String           // OPEN, PARTIAL_CLOSE, CLOSE, UPDATE, NOTE
  quantity  Decimal?
  price     Decimal
  notes     String?
  createdAt DateTime        @default(now())
  
  trade     Trade @relation(fields: [tradeId], references: [id], onDelete: Cascade)
  
  @@map("trade_events")
}

model Like {
  id      String @id @default(cuid())
  userId  String
  tradeId String
  createdAt DateTime @default(now())
  
  user    User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  trade   Trade @relation(fields: [tradeId], references: [id], onDelete: Cascade)
  
  @@unique([userId, tradeId])
  @@map("likes")
}

model Comment {
  id        String   @id @default(cuid())
  userId    String
  tradeId   String
  content   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  user      User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  trade     Trade @relation(fields: [tradeId], references: [id], onDelete: Cascade)
  
  @@map("comments")
}

model Notification {
  id        String           @id @default(cuid())
  userId    String
  type      String           // TRADE_COPIED, NEW_FOLLOWER, TRADE_LIKED, TRADE_COMMENTED, SYSTEM
  title     String
  message   String
  read      Boolean          @default(false)
  data      String?          // JSON string for SQLite compatibility
  createdAt DateTime         @default(now())
  
  user      User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("notifications")
}

// Note: SQLite doesn't support enums, so we use String fields with comments
// AssetType: STOCK, OPTION, CRYPTO, FOREX, FUTURE, ETF, INDEX
// TradeSide: BUY, SELL, SHORT
// TradeStatus: OPEN, CLOSED, PARTIAL, CANCELLED
// TradeEventType: OPEN, PARTIAL_CLOSE, CLOSE, UPDATE, NOTE
// NotificationType: TRADE_COPIED, NEW_FOLLOWER, TRADE_LIKED, TRADE_COMMENTED, SYSTEM
