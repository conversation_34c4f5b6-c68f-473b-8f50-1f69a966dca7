import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { Providers } from '@/components/providers'
import { Toaster } from '@/components/ui/toaster'
import { cn } from '@/lib/utils'
import '@/styles/globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: {
    default: 'TradeFlow Pro - Social Trading Platform',
    template: '%s | TradeFlow Pro'
  },
  description: 'Track, share, and copy trades with the most advanced social trading platform. Join thousands of traders sharing their strategies and performance.',
  keywords: [
    'trading',
    'social trading',
    'copy trading',
    'stocks',
    'crypto',
    'forex',
    'investment',
    'portfolio tracking',
    'trading signals'
  ],
  authors: [{ name: 'TradeFlow Pro Team' }],
  creator: 'TradeFlow Pro',
  publisher: 'TradeFlow Pro',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: '/',
    title: 'TradeFlow Pro - Social Trading Platform',
    description: 'Track, share, and copy trades with the most advanced social trading platform.',
    siteName: 'TradeFlow Pro',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'TradeFlow Pro - Social Trading Platform',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'TradeFlow Pro - Social Trading Platform',
    description: 'Track, share, and copy trades with the most advanced social trading platform.',
    images: ['/og-image.png'],
    creator: '@tradeflowpro',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#000000" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
      </head>
      <body className={cn(
        "min-h-screen bg-background font-sans antialiased",
        inter.className
      )}>
        <Providers>
          <div className="relative flex min-h-screen flex-col">
            <div className="flex-1">
              {children}
            </div>
          </div>
          <Toaster />
        </Providers>
      </body>
    </html>
  )
}
