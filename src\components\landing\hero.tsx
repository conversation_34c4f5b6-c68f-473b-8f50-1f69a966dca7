'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { ArrowRight, TrendingUp, Users, BarChart3, Zap } from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'

const stats = [
  { label: 'Active Traders', value: '50K+', icon: Users },
  { label: 'Trades Tracked', value: '2M+', icon: BarChart3 },
  { label: 'Success Rate', value: '87%', icon: TrendingUp },
  { label: 'Real-time Updates', value: '24/7', icon: Zap },
]

const tradingAssets = [
  { symbol: 'AAPL', name: 'Apple Inc.', price: '$185.92', change: '+2.34%', positive: true },
  { symbol: 'TSLA', name: 'Tesla Inc.', price: '$248.50', change: '-1.23%', positive: false },
  { symbol: 'BTC', name: 'Bitcoin', price: '$43,250', change: '+5.67%', positive: true },
  { symbol: 'NVDA', name: 'NVIDIA Corp.', price: '$722.48', change: '+3.45%', positive: true },
  { symbol: 'ETH', name: 'Ethereum', price: '$2,650', change: '+2.89%', positive: true },
  { symbol: 'MSFT', name: 'Microsoft Corp.', price: '$378.85', change: '+1.56%', positive: true },
]

export function Hero() {
  const [currentAssetIndex, setCurrentAssetIndex] = useState(0)

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentAssetIndex((prev) => (prev + 1) % tradingAssets.length)
    }, 3000)
    return () => clearInterval(interval)
  }, [])

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-background via-background to-primary/5 pt-20 pb-16">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:50px_50px]" />
      <div className="absolute top-0 right-0 -translate-y-12 translate-x-12">
        <div className="h-96 w-96 rounded-full bg-primary/10 blur-3xl" />
      </div>
      <div className="absolute bottom-0 left-0 translate-y-12 -translate-x-12">
        <div className="h-96 w-96 rounded-full bg-profit/10 blur-3xl" />
      </div>

      <div className="container relative mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center lg:text-left"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="inline-flex items-center rounded-full bg-primary/10 px-4 py-2 text-sm font-medium text-primary mb-6"
            >
              <TrendingUp className="mr-2 h-4 w-4" />
              #1 Social Trading Platform
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight text-foreground mb-6"
            >
              Track, Share &{' '}
              <span className="bg-gradient-to-r from-primary to-profit bg-clip-text text-transparent">
                Copy Trades
              </span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="text-lg sm:text-xl text-muted-foreground mb-8 max-w-2xl"
            >
              Join thousands of traders sharing their strategies and performance. 
              Track your trades with advanced analytics, follow successful traders, 
              and copy their winning strategies automatically.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="flex flex-col sm:flex-row gap-4 mb-12"
            >
              <Button asChild size="xl" className="group">
                <Link href="/auth/signup">
                  Start Trading Now
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>
              <Button asChild variant="outline" size="xl">
                <Link href="/demo">
                  View Demo
                </Link>
              </Button>
            </motion.div>

            {/* Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="grid grid-cols-2 lg:grid-cols-4 gap-6"
            >
              {stats.map((stat, index) => (
                <div key={stat.label} className="text-center lg:text-left">
                  <div className="flex items-center justify-center lg:justify-start mb-2">
                    <stat.icon className="h-5 w-5 text-primary mr-2" />
                    <span className="text-2xl font-bold text-foreground">{stat.value}</span>
                  </div>
                  <p className="text-sm text-muted-foreground">{stat.label}</p>
                </div>
              ))}
            </motion.div>
          </motion.div>

          {/* Right Column - Trading Dashboard Preview */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="relative"
          >
            <div className="relative rounded-2xl bg-card border shadow-2xl overflow-hidden">
              {/* Mock Trading Interface */}
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold">Live Trades</h3>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <div className="h-2 w-2 bg-profit rounded-full mr-2 animate-pulse" />
                    Live
                  </div>
                </div>

                <div className="space-y-4">
                  {tradingAssets.slice(0, 4).map((asset, index) => (
                    <motion.div
                      key={asset.symbol}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: 0.6 + index * 0.1 }}
                      className={`flex items-center justify-between p-3 rounded-lg border transition-colors ${
                        index === currentAssetIndex ? 'bg-primary/5 border-primary/20' : 'bg-muted/30'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                          <span className="text-xs font-bold text-primary">
                            {asset.symbol.slice(0, 2)}
                          </span>
                        </div>
                        <div>
                          <p className="font-medium text-sm">{asset.symbol}</p>
                          <p className="text-xs text-muted-foreground">{asset.name}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-sm">{asset.price}</p>
                        <p className={`text-xs ${
                          asset.positive ? 'text-profit' : 'text-loss'
                        }`}>
                          {asset.change}
                        </p>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Floating Elements */}
              <motion.div
                animate={{ y: [0, -10, 0] }}
                transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
                className="absolute -top-4 -right-4 bg-profit text-white px-3 py-2 rounded-lg text-sm font-medium shadow-lg"
              >
                +$2,450 Today
              </motion.div>

              <motion.div
                animate={{ y: [0, 10, 0] }}
                transition={{ duration: 4, repeat: Infinity, ease: "easeInOut", delay: 1 }}
                className="absolute -bottom-4 -left-4 bg-primary text-primary-foreground px-3 py-2 rounded-lg text-sm font-medium shadow-lg"
              >
                87% Win Rate
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
