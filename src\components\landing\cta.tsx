'use client'

import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { ArrowRight, CheckCircle, Zap, TrendingUp } from 'lucide-react'
import Link from 'next/link'

const benefits = [
  'Free to start - no credit card required',
  'Connect unlimited trading accounts',
  'Access to premium analytics tools',
  '24/7 customer support',
  'Join 50,000+ active traders',
]

const features = [
  {
    icon: TrendingUp,
    text: 'Track all your trades',
  },
  {
    icon: Zap,
    text: 'Real-time market data',
  },
  {
    icon: CheckCircle,
    text: 'Copy successful traders',
  },
]

export function CTA() {
  return (
    <section className="py-24 bg-gradient-to-br from-primary/10 via-background to-profit/10 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:50px_50px]" />
      <div className="absolute top-0 right-0 -translate-y-12 translate-x-12">
        <div className="h-96 w-96 rounded-full bg-primary/20 blur-3xl" />
      </div>
      <div className="absolute bottom-0 left-0 translate-y-12 -translate-x-12">
        <div className="h-96 w-96 rounded-full bg-profit/20 blur-3xl" />
      </div>

      <div className="container relative mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-6">
              Ready to{' '}
              <span className="bg-gradient-to-r from-primary to-profit bg-clip-text text-transparent">
                Transform
              </span>{' '}
              Your Trading?
            </h2>
            <p className="text-lg sm:text-xl text-muted-foreground max-w-2xl mx-auto mb-8">
              Join thousands of successful traders who use TradeFlow Pro to track, 
              analyze, and improve their trading performance every day.
            </p>

            {/* Quick features */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="flex flex-wrap justify-center items-center gap-6 mb-8"
            >
              {features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <feature.icon className="h-4 w-4 text-primary" />
                  <span>{feature.text}</span>
                </div>
              ))}
            </motion.div>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left side - Benefits */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <h3 className="text-2xl font-bold text-foreground mb-6">
                Everything you need to succeed
              </h3>
              <ul className="space-y-4">
                {benefits.map((benefit, index) => (
                  <motion.li
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.4, delay: 0.4 + index * 0.1 }}
                    className="flex items-center space-x-3"
                  >
                    <CheckCircle className="h-5 w-5 text-profit flex-shrink-0" />
                    <span className="text-muted-foreground">{benefit}</span>
                  </motion.li>
                ))}
              </ul>

              {/* Trust indicators */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.8 }}
                className="mt-8 p-4 bg-card/50 border rounded-lg"
              >
                <div className="flex items-center justify-between text-sm">
                  <div className="text-center">
                    <div className="font-bold text-foreground">4.9/5</div>
                    <div className="text-muted-foreground">Rating</div>
                  </div>
                  <div className="text-center">
                    <div className="font-bold text-foreground">50K+</div>
                    <div className="text-muted-foreground">Users</div>
                  </div>
                  <div className="text-center">
                    <div className="font-bold text-foreground">99.9%</div>
                    <div className="text-muted-foreground">Uptime</div>
                  </div>
                </div>
              </motion.div>
            </motion.div>

            {/* Right side - CTA */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="text-center lg:text-left"
            >
              <div className="bg-card border rounded-2xl p-8 shadow-lg">
                <div className="mb-6">
                  <h4 className="text-xl font-bold text-foreground mb-2">
                    Start Your Free Account
                  </h4>
                  <p className="text-muted-foreground">
                    No credit card required. Get started in less than 2 minutes.
                  </p>
                </div>

                <div className="space-y-4">
                  <Button asChild size="lg" className="w-full group">
                    <Link href="/auth/signup">
                      Create Free Account
                      <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                    </Link>
                  </Button>

                  <div className="text-center">
                    <span className="text-sm text-muted-foreground">or</span>
                  </div>

                  <Button asChild variant="outline" size="lg" className="w-full">
                    <Link href="/demo">
                      Try Demo First
                    </Link>
                  </Button>
                </div>

                <div className="mt-6 text-center">
                  <p className="text-xs text-muted-foreground">
                    Already have an account?{' '}
                    <Link href="/auth/signin" className="text-primary hover:underline">
                      Sign in here
                    </Link>
                  </p>
                </div>
              </div>

              {/* Additional CTA elements */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.9 }}
                className="mt-6 flex items-center justify-center lg:justify-start space-x-4 text-sm text-muted-foreground"
              >
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-profit rounded-full animate-pulse" />
                  <span>2,847 traders joined this week</span>
                </div>
              </motion.div>
            </motion.div>
          </div>

          {/* Bottom section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 1.0 }}
            className="text-center mt-16"
          >
            <p className="text-sm text-muted-foreground">
              Questions? Contact our support team at{' '}
              <a href="mailto:<EMAIL>" className="text-primary hover:underline">
                <EMAIL>
              </a>
            </p>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
