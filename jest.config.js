const nextJest = require('next/jest')

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files
  dir: './',
})

// Add any custom config to be passed to Jest
const customJestConfig = {
  // Add more setup options before each test is run
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  
  // Test environment
  testEnvironment: 'jest-environment-jsdom',
  
  // Module name mapping for absolute imports
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@/components/(.*)$': '<rootDir>/src/components/$1',
    '^@/lib/(.*)$': '<rootDir>/src/lib/$1',
    '^@/hooks/(.*)$': '<rootDir>/src/hooks/$1',
    '^@/types/(.*)$': '<rootDir>/src/lib/types/$1',
    '^@/utils/(.*)$': '<rootDir>/src/lib/utils/$1',
    '^@/styles/(.*)$': '<rootDir>/src/styles/$1',
  },
  
  // Test patterns
  testMatch: [
    '<rootDir>/**/__tests__/**/*.{js,jsx,ts,tsx}',
    '<rootDir>/**/*.(test|spec).{js,jsx,ts,tsx}'
  ],
  
  // Files to ignore
  testPathIgnorePatterns: [
    '<rootDir>/.next/',
    '<rootDir>/node_modules/',
    '<rootDir>/build/',
    '<rootDir>/dist/',
  ],
  
  // Transform configuration
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': ['babel-jest', { presets: ['next/babel'] }],
  },
  
  // Module file extensions
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  
  // Coverage configuration
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{js,jsx,ts,tsx}',
    '!src/**/index.{js,jsx,ts,tsx}',
    '!src/styles/**',
    '!src/components/ui/**', // UI components are mostly styling
  ],
  
  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
    // Specific thresholds for critical modules
    './src/lib/auth.ts': {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
    './src/lib/rate-limit.ts': {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
    './src/app/api/trades/route.ts': {
      branches: 75,
      functions: 75,
      lines: 75,
      statements: 75,
    },
  },
  
  // Coverage reporters
  coverageReporters: ['text', 'lcov', 'html', 'json-summary'],
  
  // Test timeout
  testTimeout: 10000,
  
  // Global setup and teardown
  globalSetup: '<rootDir>/tests/setup/global-setup.js',
  globalTeardown: '<rootDir>/tests/setup/global-teardown.js',
  
  // Clear mocks between tests
  clearMocks: true,
  restoreMocks: true,
  
  // Verbose output
  verbose: true,
  
  // Environment variables for tests
  setupFiles: ['<rootDir>/tests/setup/env.js'],
}

// API tests configuration (separate environment)
const apiTestConfig = {
  ...customJestConfig,
  displayName: 'API Tests',
  testEnvironment: 'node',
  testMatch: ['<rootDir>/tests/api/**/*.test.{js,ts}'],
  setupFilesAfterEnv: ['<rootDir>/tests/setup/api-setup.js'],
}

// Component tests configuration
const componentTestConfig = {
  ...customJestConfig,
  displayName: 'Component Tests',
  testEnvironment: 'jest-environment-jsdom',
  testMatch: ['<rootDir>/tests/components/**/*.test.{js,jsx,ts,tsx}'],
  setupFilesAfterEnv: ['<rootDir>/tests/setup/component-setup.js'],
}

// Integration tests configuration
const integrationTestConfig = {
  ...customJestConfig,
  displayName: 'Integration Tests',
  testEnvironment: 'node',
  testMatch: ['<rootDir>/tests/integration/**/*.test.{js,ts}'],
  setupFilesAfterEnv: ['<rootDir>/tests/setup/integration-setup.js'],
  testTimeout: 30000, // Longer timeout for integration tests
}

// Export configuration based on environment
module.exports = process.env.TEST_TYPE === 'api' 
  ? createJestConfig(apiTestConfig)
  : process.env.TEST_TYPE === 'components'
  ? createJestConfig(componentTestConfig)
  : process.env.TEST_TYPE === 'integration'
  ? createJestConfig(integrationTestConfig)
  : createJestConfig(customJestConfig)
