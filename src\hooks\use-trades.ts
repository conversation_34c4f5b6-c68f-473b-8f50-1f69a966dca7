'use client'

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useSession } from 'next-auth/react'
import { useToast } from '@/components/ui/toaster'
import axios from 'axios'

// Types
interface Trade {
  id: string
  symbol: string
  assetType: string
  side: 'BUY' | 'SELL' | 'SHORT'
  quantity: number
  entryPrice: number
  currentPrice?: number
  exitPrice?: number
  status: 'OPEN' | 'CLOSED' | 'PARTIAL'
  entryDate: string
  exitDate?: string
  realizedPnl?: number
  unrealizedPnl?: number
  notes?: string
  tags: string[]
  isPublic: boolean
  user: {
    id: string
    name: string
    username: string
    avatar?: string
  }
  _count: {
    likes: number
    comments: number
    copiedTrades: number
  }
}

interface CreateTradeData {
  symbol: string
  assetType: string
  side: string
  quantity: number
  entryPrice: number
  entryDate: string
  portfolioId?: string
  notes?: string
  tags?: string[]
  isPublic?: boolean
}

interface TradesResponse {
  trades: Trade[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// Custom hook for fetching trades
export function useTrades(params?: {
  page?: number
  limit?: number
  status?: string
  assetType?: string
  portfolioId?: string
}) {
  const { data: session } = useSession()

  return useQuery({
    queryKey: ['trades', params],
    queryFn: async (): Promise<TradesResponse> => {
      const searchParams = new URLSearchParams()
      
      if (params?.page) searchParams.set('page', params.page.toString())
      if (params?.limit) searchParams.set('limit', params.limit.toString())
      if (params?.status) searchParams.set('status', params.status)
      if (params?.assetType) searchParams.set('assetType', params.assetType)
      if (params?.portfolioId) searchParams.set('portfolioId', params.portfolioId)

      const response = await axios.get(`/api/trades?${searchParams.toString()}`)
      return response.data
    },
    enabled: !!session,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // Refetch every minute for real-time updates
  })
}

// Custom hook for creating trades
export function useCreateTrade() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: CreateTradeData): Promise<Trade> => {
      const response = await axios.post('/api/trades', data)
      return response.data
    },
    onSuccess: (newTrade) => {
      // Invalidate and refetch trades
      queryClient.invalidateQueries({ queryKey: ['trades'] })
      
      // Optimistically update the cache
      queryClient.setQueryData(['trades'], (old: TradesResponse | undefined) => {
        if (!old) return old
        return {
          ...old,
          trades: [newTrade, ...old.trades],
        }
      })

      toast.success('Trade created successfully!')
    },
    onError: (error: any) => {
      const message = error.response?.data?.error || 'Failed to create trade'
      toast.error('Error creating trade', message)
    },
  })
}

// Custom hook for updating trades
export function useUpdateTrade() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<CreateTradeData> }): Promise<Trade> => {
      const response = await axios.put(`/api/trades/${id}`, data)
      return response.data
    },
    onSuccess: (updatedTrade) => {
      // Update the specific trade in cache
      queryClient.setQueryData(['trades'], (old: TradesResponse | undefined) => {
        if (!old) return old
        return {
          ...old,
          trades: old.trades.map(trade => 
            trade.id === updatedTrade.id ? updatedTrade : trade
          ),
        }
      })

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['trades'] })
      queryClient.invalidateQueries({ queryKey: ['trade', updatedTrade.id] })

      toast.success('Trade updated successfully!')
    },
    onError: (error: any) => {
      const message = error.response?.data?.error || 'Failed to update trade'
      toast.error('Error updating trade', message)
    },
  })
}

// Custom hook for deleting trades
export function useDeleteTrade() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (id: string): Promise<void> => {
      await axios.delete(`/api/trades/${id}`)
    },
    onSuccess: (_, deletedId) => {
      // Remove the trade from cache
      queryClient.setQueryData(['trades'], (old: TradesResponse | undefined) => {
        if (!old) return old
        return {
          ...old,
          trades: old.trades.filter(trade => trade.id !== deletedId),
        }
      })

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['trades'] })

      toast.success('Trade deleted successfully!')
    },
    onError: (error: any) => {
      const message = error.response?.data?.error || 'Failed to delete trade'
      toast.error('Error deleting trade', message)
    },
  })
}

// Custom hook for liking/unliking trades
export function useLikeTrade() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ tradeId, isLiked }: { tradeId: string; isLiked: boolean }): Promise<void> => {
      if (isLiked) {
        await axios.delete(`/api/trades/${tradeId}/like`)
      } else {
        await axios.post(`/api/trades/${tradeId}/like`)
      }
    },
    onMutate: async ({ tradeId, isLiked }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['trades'] })

      // Snapshot previous value
      const previousTrades = queryClient.getQueryData(['trades'])

      // Optimistically update
      queryClient.setQueryData(['trades'], (old: TradesResponse | undefined) => {
        if (!old) return old
        return {
          ...old,
          trades: old.trades.map(trade => 
            trade.id === tradeId 
              ? {
                  ...trade,
                  _count: {
                    ...trade._count,
                    likes: isLiked ? trade._count.likes - 1 : trade._count.likes + 1
                  },
                  isLiked: !isLiked
                }
              : trade
          ),
        }
      })

      return { previousTrades }
    },
    onError: (error, variables, context) => {
      // Rollback on error
      if (context?.previousTrades) {
        queryClient.setQueryData(['trades'], context.previousTrades)
      }
      toast.error('Failed to update like status')
    },
    onSettled: () => {
      // Refetch to ensure consistency
      queryClient.invalidateQueries({ queryKey: ['trades'] })
    },
  })
}

// Custom hook for copying trades
export function useCopyTrade() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (tradeId: string): Promise<Trade> => {
      const response = await axios.post(`/api/trades/${tradeId}/copy`)
      return response.data
    },
    onSuccess: (copiedTrade) => {
      // Add the copied trade to the user's trades
      queryClient.setQueryData(['trades'], (old: TradesResponse | undefined) => {
        if (!old) return old
        return {
          ...old,
          trades: [copiedTrade, ...old.trades],
        }
      })

      // Invalidate trades to refetch
      queryClient.invalidateQueries({ queryKey: ['trades'] })

      toast.success('Trade copied successfully!')
    },
    onError: (error: any) => {
      const message = error.response?.data?.error || 'Failed to copy trade'
      toast.error('Error copying trade', message)
    },
  })
}

// Custom hook for getting a single trade
export function useTrade(id: string) {
  return useQuery({
    queryKey: ['trade', id],
    queryFn: async (): Promise<Trade> => {
      const response = await axios.get(`/api/trades/${id}`)
      return response.data
    },
    enabled: !!id,
    staleTime: 30 * 1000,
  })
}
