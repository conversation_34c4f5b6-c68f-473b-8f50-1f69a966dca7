import * as React from "react"
import { cn } from "@/lib/utils"

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: string
  label?: string
  helperText?: string
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, error, label, helperText, leftIcon, rightIcon, ...props }, ref) => {
    const inputId = React.useId()
    
    return (
      <div className="space-y-2">
        {label && (
          <label 
            htmlFor={inputId}
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            {label}
          </label>
        )}
        
        <div className="relative">
          {leftIcon && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
              {leftIcon}
            </div>
          )}
          
          <input
            id={inputId}
            type={type}
            className={cn(
              "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
              leftIcon && "pl-10",
              rightIcon && "pr-10",
              error && "border-destructive focus-visible:ring-destructive",
              className
            )}
            ref={ref}
            {...props}
          />
          
          {rightIcon && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
              {rightIcon}
            </div>
          )}
        </div>
        
        {(error || helperText) && (
          <p className={cn(
            "text-sm",
            error ? "text-destructive" : "text-muted-foreground"
          )}>
            {error || helperText}
          </p>
        )}
      </div>
    )
  }
)
Input.displayName = "Input"

// Number input with trading-specific formatting
interface NumberInputProps extends Omit<InputProps, 'type' | 'onChange'> {
  value?: number
  onChange?: (value: number | undefined) => void
  min?: number
  max?: number
  step?: number
  precision?: number
  prefix?: string
  suffix?: string
}

const NumberInput = React.forwardRef<HTMLInputElement, NumberInputProps>(
  ({ 
    value, 
    onChange, 
    min, 
    max, 
    step = 0.01, 
    precision = 2, 
    prefix, 
    suffix,
    className,
    ...props 
  }, ref) => {
    const [displayValue, setDisplayValue] = React.useState(
      value !== undefined ? value.toFixed(precision) : ''
    )

    React.useEffect(() => {
      if (value !== undefined) {
        setDisplayValue(value.toFixed(precision))
      }
    }, [value, precision])

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value
      setDisplayValue(inputValue)

      // Parse the number
      const numValue = parseFloat(inputValue)
      
      if (isNaN(numValue)) {
        onChange?.(undefined)
      } else {
        // Apply min/max constraints
        let constrainedValue = numValue
        if (min !== undefined && constrainedValue < min) {
          constrainedValue = min
        }
        if (max !== undefined && constrainedValue > max) {
          constrainedValue = max
        }
        
        onChange?.(constrainedValue)
      }
    }

    const handleBlur = () => {
      if (value !== undefined) {
        setDisplayValue(value.toFixed(precision))
      }
    }

    return (
      <div className="relative">
        {prefix && (
          <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground text-sm">
            {prefix}
          </span>
        )}
        
        <Input
          ref={ref}
          type="number"
          value={displayValue}
          onChange={handleChange}
          onBlur={handleBlur}
          min={min}
          max={max}
          step={step}
          className={cn(
            prefix && "pl-8",
            suffix && "pr-8",
            className
          )}
          {...props}
        />
        
        {suffix && (
          <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground text-sm">
            {suffix}
          </span>
        )}
      </div>
    )
  }
)
NumberInput.displayName = "NumberInput"

// Search input with clear button
interface SearchInputProps extends Omit<InputProps, 'type'> {
  onClear?: () => void
  showClearButton?: boolean
}

const SearchInput = React.forwardRef<HTMLInputElement, SearchInputProps>(
  ({ onClear, showClearButton = true, rightIcon, ...props }, ref) => {
    const [value, setValue] = React.useState(props.value || '')

    const handleClear = () => {
      setValue('')
      onClear?.()
    }

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setValue(e.target.value)
      props.onChange?.(e)
    }

    return (
      <Input
        ref={ref}
        type="search"
        value={value}
        onChange={handleChange}
        rightIcon={
          showClearButton && value ? (
            <button
              type="button"
              onClick={handleClear}
              className="text-muted-foreground hover:text-foreground"
            >
              ×
            </button>
          ) : rightIcon
        }
        {...props}
      />
    )
  }
)
SearchInput.displayName = "SearchInput"

// Password input with visibility toggle
const PasswordInput = React.forwardRef<HTMLInputElement, InputProps>(
  ({ ...props }, ref) => {
    const [showPassword, setShowPassword] = React.useState(false)

    return (
      <Input
        ref={ref}
        type={showPassword ? "text" : "password"}
        rightIcon={
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="text-muted-foreground hover:text-foreground"
          >
            {showPassword ? "👁️" : "👁️‍🗨️"}
          </button>
        }
        {...props}
      />
    )
  }
)
PasswordInput.displayName = "PasswordInput"

export { Input, NumberInput, SearchInput, PasswordInput }
