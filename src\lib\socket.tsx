'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { io, Socket } from 'socket.io-client'
import { useSession } from 'next-auth/react'

interface SocketContextType {
  socket: Socket | null
  isConnected: boolean
  emit: (event: string, data?: any) => void
  on: (event: string, callback: (data: any) => void) => void
  off: (event: string, callback?: (data: any) => void) => void
}

const SocketContext = createContext<SocketContextType>({
  socket: null,
  isConnected: false,
  emit: () => {},
  on: () => {},
  off: () => {},
})

export const useSocket = () => {
  const context = useContext(SocketContext)
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider')
  }
  return context
}

interface SocketProviderProps {
  children: ReactNode
}

export function SocketProvider({ children }: SocketProviderProps) {
  const [socket, setSocket] = useState<Socket | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const { data: session, status } = useSession()

  useEffect(() => {
    if (status === 'loading') return

    // Initialize socket connection
    const socketInstance = io(process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3000', {
      path: '/api/socket',
      addTrailingSlash: false,
      transports: ['websocket', 'polling'],
      auth: {
        token: session?.user?.id || null,
      },
    })

    // Connection event handlers
    socketInstance.on('connect', () => {
      console.log('✅ Socket connected:', socketInstance.id)
      setIsConnected(true)
      
      // Join user room if authenticated
      if (session?.user?.id) {
        socketInstance.emit('join-user-room', session.user.id)
      }
    })

    socketInstance.on('disconnect', (reason) => {
      console.log('❌ Socket disconnected:', reason)
      setIsConnected(false)
    })

    socketInstance.on('connect_error', (error) => {
      console.error('Socket connection error:', error)
      setIsConnected(false)
    })

    socketInstance.on('reconnect', (attemptNumber) => {
      console.log('🔄 Socket reconnected after', attemptNumber, 'attempts')
      setIsConnected(true)
    })

    socketInstance.on('reconnect_error', (error) => {
      console.error('Socket reconnection error:', error)
    })

    // Set socket instance
    setSocket(socketInstance)

    // Cleanup on unmount
    return () => {
      console.log('🧹 Cleaning up socket connection')
      socketInstance.disconnect()
      setSocket(null)
      setIsConnected(false)
    }
  }, [session?.user?.id, status])

  // Helper functions
  const emit = (event: string, data?: any) => {
    if (socket && isConnected) {
      socket.emit(event, data)
    } else {
      console.warn('Socket not connected, cannot emit event:', event)
    }
  }

  const on = (event: string, callback: (data: any) => void) => {
    if (socket) {
      socket.on(event, callback)
    }
  }

  const off = (event: string, callback?: (data: any) => void) => {
    if (socket) {
      if (callback) {
        socket.off(event, callback)
      } else {
        socket.off(event)
      }
    }
  }

  const value: SocketContextType = {
    socket,
    isConnected,
    emit,
    on,
    off,
  }

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  )
}

// Custom hooks for specific socket events
export function useTradeUpdates(callback: (trade: any) => void) {
  const { on, off } = useSocket()

  useEffect(() => {
    on('trade-update', callback)
    return () => off('trade-update', callback)
  }, [on, off, callback])
}

export function useMarketUpdates(callback: (data: any) => void) {
  const { on, off } = useSocket()

  useEffect(() => {
    on('market-update', callback)
    return () => off('market-update', callback)
  }, [on, off, callback])
}

export function useNotifications(callback: (notification: any) => void) {
  const { on, off } = useSocket()

  useEffect(() => {
    on('notification', callback)
    return () => off('notification', callback)
  }, [on, off, callback])
}

export function useFollowerUpdates(callback: (data: any) => void) {
  const { on, off } = useSocket()

  useEffect(() => {
    on('follower-update', callback)
    return () => off('follower-update', callback)
  }, [on, off, callback])
}

// Socket event types for type safety
export interface TradeUpdateEvent {
  type: 'trade-created' | 'trade-updated' | 'trade-closed'
  trade: any
  userId: string
}

export interface MarketUpdateEvent {
  symbol: string
  price: number
  change: number
  changePercent: number
  timestamp: string
}

export interface NotificationEvent {
  id: string
  type: string
  title: string
  message: string
  userId: string
  createdAt: string
}

export interface FollowerUpdateEvent {
  type: 'new-follower' | 'unfollowed'
  followerId: string
  followingId: string
  followerData?: any
}
