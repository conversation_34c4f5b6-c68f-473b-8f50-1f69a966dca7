# Database
DATABASE_URL="postgresql://username:password@localhost:5432/tradeflow_pro"
REDIS_URL="redis://localhost:6379"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# OAuth Providers
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"

# Financial Data APIs
ALPHA_VANTAGE_API_KEY="your-alpha-vantage-api-key"
POLYGON_API_KEY="your-polygon-api-key"
FINNHUB_API_KEY="your-finnhub-api-key"
COINGECKO_API_KEY="your-coingecko-api-key"

# Social Media APIs
TWITTER_API_KEY="your-twitter-api-key"
TWITTER_API_SECRET="your-twitter-api-secret"
TWITTER_ACCESS_TOKEN="your-twitter-access-token"
TWITTER_ACCESS_TOKEN_SECRET="your-twitter-access-token-secret"

# Discord
DISCORD_WEBHOOK_URL="your-discord-webhook-url"
DISCORD_BOT_TOKEN="your-discord-bot-token"

# File Storage
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="tradeflow-pro-assets"

# Or use Cloudinary
CLOUDINARY_CLOUD_NAME="your-cloudinary-cloud-name"
CLOUDINARY_API_KEY="your-cloudinary-api-key"
CLOUDINARY_API_SECRET="your-cloudinary-api-secret"

# Email Service (Optional)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Application Settings
NODE_ENV="development"
PORT="3000"
APP_URL="http://localhost:3000"

# Rate Limiting
RATE_LIMIT_MAX="100"
RATE_LIMIT_WINDOW="900000"

# WebSocket
SOCKET_PORT="3001"

# Analytics (Optional)
GOOGLE_ANALYTICS_ID="G-XXXXXXXXXX"
MIXPANEL_TOKEN="your-mixpanel-token"

# Monitoring (Optional)
SENTRY_DSN="your-sentry-dsn"

# Feature Flags
ENABLE_COPY_TRADING="true"
ENABLE_PAPER_TRADING="true"
ENABLE_SOCIAL_SHARING="true"
ENABLE_REAL_TIME_UPDATES="true"
