import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

// GET /api/users/[id] - Get user profile by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    const userId = params.id

    // Get user with stats
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        username: true,
        name: true,
        bio: true,
        avatar: true,
        verified: true,
        isPublic: true,
        allowCopying: true,
        createdAt: true,
        _count: {
          select: {
            followers: true,
            following: true,
            trades: true,
          }
        }
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Check if profile is public or if it's the current user
    if (!user.isPublic && session?.user?.id !== userId) {
      return NextResponse.json(
        { error: 'Profile is private' },
        { status: 403 }
      )
    }

    // Get additional stats for the user
    const [
      totalPnL,
      winRate,
      isFollowing
    ] = await Promise.all([
      // Calculate total P&L
      prisma.trade.aggregate({
        where: {
          userId,
          status: 'CLOSED',
          realizedPnl: { not: null }
        },
        _sum: {
          realizedPnl: true
        }
      }),
      
      // Calculate win rate
      prisma.$queryRaw<[{ winRate: number }]>`
        SELECT 
          CASE 
            WHEN COUNT(*) = 0 THEN 0
            ELSE (COUNT(CASE WHEN "realizedPnl" > 0 THEN 1 END) * 100.0 / COUNT(*))
          END as "winRate"
        FROM "trades" 
        WHERE "userId" = ${userId} 
        AND "status" = 'CLOSED' 
        AND "realizedPnl" IS NOT NULL
      `,
      
      // Check if current user is following this user
      session?.user?.id ? prisma.follow.findUnique({
        where: {
          followerId_followingId: {
            followerId: session.user.id,
            followingId: userId
          }
        }
      }) : null
    ])

    const userWithStats = {
      ...user,
      stats: {
        totalPnL: totalPnL._sum.realizedPnl || 0,
        winRate: winRate[0]?.winRate || 0,
        totalTrades: user._count.trades,
        followers: user._count.followers,
        following: user._count.following,
      },
      isFollowing: !!isFollowing
    }

    return NextResponse.json(userWithStats)

  } catch (error) {
    console.error('Error fetching user profile:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET /api/users/[id]/trades - Get user's public trades
export async function getUserTrades(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    const userId = params.id
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')

    const skip = (page - 1) * limit

    // Check if user exists and profile is public
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { isPublic: true }
    })

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    if (!user.isPublic && session?.user?.id !== userId) {
      return NextResponse.json(
        { error: 'Profile is private' },
        { status: 403 }
      )
    }

    // Get trades
    const where: any = {
      userId,
      isPublic: true
    }

    // If it's the user's own profile, show all trades
    if (session?.user?.id === userId) {
      delete where.isPublic
    }

    const [trades, total] = await Promise.all([
      prisma.trade.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              name: true,
              avatar: true,
              verified: true
            }
          },
          _count: {
            select: {
              likes: true,
              comments: true,
              copiedTrades: true,
            }
          }
        }
      }),
      prisma.trade.count({ where })
    ])

    return NextResponse.json({
      trades,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      }
    })

  } catch (error) {
    console.error('Error fetching user trades:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
