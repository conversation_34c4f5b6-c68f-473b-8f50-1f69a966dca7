# TradeFlow Pro - Deployment Guide

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- PostgreSQL 14+
- Redis 6+
- Git

### Local Development Setup

1. **Clone the repository**
```bash
git clone https://github.com/yourusername/tradeflow-pro.git
cd tradeflow-pro
```

2. **Install dependencies**
```bash
npm install
```

3. **Set up environment variables**
```bash
cp .env.example .env.local
```

Edit `.env.local` with your configuration:
```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/tradeflow_pro"
REDIS_URL="redis://localhost:6379"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# API Keys (get from respective providers)
ALPHA_VANTAGE_API_KEY="your-alpha-vantage-key"
POLYGON_API_KEY="your-polygon-key"
COINGECKO_API_KEY="your-coingecko-key"

# OAuth (optional for development)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
```

4. **Set up the database**
```bash
# Generate Prisma client
npx prisma generate

# Run migrations
npx prisma migrate dev

# Seed with demo data
npx prisma db seed
```

5. **Start development server**
```bash
npm run dev
```

Visit `http://localhost:3000` to see the application.

## 🌐 Production Deployment

### Option 1: Vercel (Recommended)

1. **Deploy to Vercel**
```bash
npm install -g vercel
vercel
```

2. **Set up environment variables in Vercel dashboard**
- Go to your project settings
- Add all environment variables from `.env.example`
- Set `NEXTAUTH_URL` to your production domain

3. **Set up database**
- Use Vercel Postgres or external PostgreSQL service
- Run migrations: `npx prisma migrate deploy`
- Seed data: `npx prisma db seed`

### Option 2: Docker Deployment

1. **Create Dockerfile**
```dockerfile
FROM node:18-alpine AS deps
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS builder
WORKDIR /app
COPY . .
COPY --from=deps /app/node_modules ./node_modules
RUN npm run build

FROM node:18-alpine AS runner
WORKDIR /app
ENV NODE_ENV production
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

EXPOSE 3000
CMD ["node", "server.js"]
```

2. **Create docker-compose.yml**
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=**************************************/tradeflow_pro
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis

  db:
    image: postgres:14
    environment:
      POSTGRES_DB: tradeflow_pro
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:6-alpine
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

3. **Deploy**
```bash
docker-compose up -d
```

### Option 3: AWS/DigitalOcean

1. **Set up server**
```bash
# Install Node.js, PostgreSQL, Redis
sudo apt update
sudo apt install nodejs npm postgresql redis-server

# Clone and setup project
git clone https://github.com/yourusername/tradeflow-pro.git
cd tradeflow-pro
npm install
npm run build
```

2. **Set up process manager**
```bash
npm install -g pm2
pm2 start npm --name "tradeflow-pro" -- start
pm2 startup
pm2 save
```

3. **Set up reverse proxy (Nginx)**
```nginx
server {
    listen 80;
    server_name yourdomain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 🔧 Configuration

### API Keys Setup

1. **Alpha Vantage** (Stock Data)
   - Visit: https://www.alphavantage.co/support/#api-key
   - Free tier: 5 API requests per minute, 500 per day

2. **Polygon.io** (Market Data)
   - Visit: https://polygon.io/
   - Free tier: 5 API calls per minute

3. **CoinGecko** (Crypto Data)
   - Visit: https://www.coingecko.com/en/api
   - Free tier: 10-50 calls/minute

4. **OAuth Providers**
   - Google: https://console.developers.google.com/
   - GitHub: https://github.com/settings/applications/new

### Database Configuration

For production, consider:
- **Supabase**: Managed PostgreSQL with built-in auth
- **PlanetScale**: Serverless MySQL platform
- **AWS RDS**: Managed relational database service
- **DigitalOcean Managed Databases**: Simple managed databases

### Redis Configuration

For production caching:
- **Upstash**: Serverless Redis
- **Redis Cloud**: Managed Redis service
- **AWS ElastiCache**: Managed in-memory data store

## 📊 Monitoring & Analytics

### Error Tracking
```bash
npm install @sentry/nextjs
```

Add to `next.config.js`:
```javascript
const { withSentryConfig } = require('@sentry/nextjs');

module.exports = withSentryConfig(nextConfig, {
  silent: true,
  org: "your-org",
  project: "tradeflow-pro",
});
```

### Analytics
- **Google Analytics**: Web analytics
- **Mixpanel**: Event tracking
- **PostHog**: Product analytics

### Performance Monitoring
- **Vercel Analytics**: Built-in performance monitoring
- **New Relic**: Application performance monitoring
- **DataDog**: Infrastructure monitoring

## 🔒 Security Checklist

- [ ] Set strong `NEXTAUTH_SECRET`
- [ ] Use HTTPS in production
- [ ] Set up CORS properly
- [ ] Enable rate limiting
- [ ] Secure database connections
- [ ] Use environment variables for secrets
- [ ] Set up proper CSP headers
- [ ] Enable security headers
- [ ] Regular security updates

## 🚀 Performance Optimization

### Database
- Add database indexes for frequently queried fields
- Use connection pooling
- Implement query optimization
- Set up read replicas for scaling

### Caching
- Redis for session storage
- API response caching
- Static asset CDN
- Database query caching

### Frontend
- Image optimization
- Code splitting
- Bundle analysis
- Service worker for offline support

## 📈 Scaling Considerations

### Horizontal Scaling
- Load balancer setup
- Multiple application instances
- Database sharding
- Microservices architecture

### Vertical Scaling
- Increase server resources
- Database performance tuning
- Redis memory optimization
- CDN implementation

## 🔄 CI/CD Pipeline

Example GitHub Actions workflow:
```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run build
      - run: npm run test
      - uses: vercel/action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
```

## 📞 Support

For deployment issues:
- Check the [GitHub Issues](https://github.com/yourusername/tradeflow-pro/issues)
- Join our [Discord Community](https://discord.gg/tradeflowpro)
- Email: <EMAIL>
