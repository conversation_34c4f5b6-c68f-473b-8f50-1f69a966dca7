import axios from 'axios'

// Types for market data
export interface StockQuote {
  symbol: string
  price: number
  change: number
  changePercent: number
  volume: number
  marketCap?: number
  high: number
  low: number
  open: number
  previousClose: number
  timestamp: string
}

export interface CryptoQuote {
  symbol: string
  price: number
  change24h: number
  changePercent24h: number
  volume24h: number
  marketCap: number
  high24h: number
  low24h: number
  timestamp: string
}

export interface HistoricalData {
  timestamp: string
  open: number
  high: number
  low: number
  close: number
  volume: number
}

// Alpha Vantage API client
class AlphaVantageClient {
  private apiKey: string
  private baseUrl = 'https://www.alphavantage.co/query'

  constructor() {
    this.apiKey = process.env.ALPHA_VANTAGE_API_KEY || ''
  }

  async getStockQuote(symbol: string): Promise<StockQuote> {
    try {
      const response = await axios.get(this.baseUrl, {
        params: {
          function: 'GLOBAL_QUOTE',
          symbol,
          apikey: this.apiKey,
        },
      })

      const quote = response.data['Global Quote']
      
      return {
        symbol: quote['01. symbol'],
        price: parseFloat(quote['05. price']),
        change: parseFloat(quote['09. change']),
        changePercent: parseFloat(quote['10. change percent'].replace('%', '')),
        volume: parseInt(quote['06. volume']),
        high: parseFloat(quote['03. high']),
        low: parseFloat(quote['04. low']),
        open: parseFloat(quote['02. open']),
        previousClose: parseFloat(quote['08. previous close']),
        timestamp: quote['07. latest trading day'],
      }
    } catch (error) {
      console.error('Alpha Vantage API error:', error)
      throw new Error('Failed to fetch stock quote')
    }
  }

  async getHistoricalData(symbol: string, interval: string = 'daily'): Promise<HistoricalData[]> {
    try {
      const response = await axios.get(this.baseUrl, {
        params: {
          function: 'TIME_SERIES_DAILY',
          symbol,
          apikey: this.apiKey,
        },
      })

      const timeSeries = response.data['Time Series (Daily)']
      
      return Object.entries(timeSeries).map(([date, data]: [string, any]) => ({
        timestamp: date,
        open: parseFloat(data['1. open']),
        high: parseFloat(data['2. high']),
        low: parseFloat(data['3. low']),
        close: parseFloat(data['4. close']),
        volume: parseInt(data['5. volume']),
      }))
    } catch (error) {
      console.error('Alpha Vantage historical data error:', error)
      throw new Error('Failed to fetch historical data')
    }
  }
}

// CoinGecko API client for crypto data
class CoinGeckoClient {
  private baseUrl = 'https://api.coingecko.com/api/v3'

  async getCryptoQuote(symbol: string): Promise<CryptoQuote> {
    try {
      const response = await axios.get(`${this.baseUrl}/simple/price`, {
        params: {
          ids: symbol.toLowerCase(),
          vs_currencies: 'usd',
          include_24hr_change: true,
          include_24hr_vol: true,
          include_market_cap: true,
        },
      })

      const data = response.data[symbol.toLowerCase()]
      
      return {
        symbol: symbol.toUpperCase(),
        price: data.usd,
        change24h: data.usd_24h_change || 0,
        changePercent24h: data.usd_24h_change || 0,
        volume24h: data.usd_24h_vol || 0,
        marketCap: data.usd_market_cap || 0,
        high24h: 0, // Not available in simple price endpoint
        low24h: 0,  // Not available in simple price endpoint
        timestamp: new Date().toISOString(),
      }
    } catch (error) {
      console.error('CoinGecko API error:', error)
      throw new Error('Failed to fetch crypto quote')
    }
  }

  async getCryptoHistoricalData(symbol: string, days: number = 30): Promise<HistoricalData[]> {
    try {
      const response = await axios.get(`${this.baseUrl}/coins/${symbol.toLowerCase()}/market_chart`, {
        params: {
          vs_currency: 'usd',
          days,
        },
      })

      const prices = response.data.prices
      
      return prices.map(([timestamp, price]: [number, number]) => ({
        timestamp: new Date(timestamp).toISOString(),
        open: price,
        high: price,
        low: price,
        close: price,
        volume: 0, // Volume data would need separate processing
      }))
    } catch (error) {
      console.error('CoinGecko historical data error:', error)
      throw new Error('Failed to fetch crypto historical data')
    }
  }
}

// Main market data service
export class MarketDataService {
  private alphaVantage: AlphaVantageClient
  private coinGecko: CoinGeckoClient

  constructor() {
    this.alphaVantage = new AlphaVantageClient()
    this.coinGecko = new CoinGeckoClient()
  }

  async getQuote(symbol: string, assetType: 'stock' | 'crypto'): Promise<StockQuote | CryptoQuote> {
    if (assetType === 'crypto') {
      return await this.coinGecko.getCryptoQuote(symbol)
    } else {
      return await this.alphaVantage.getStockQuote(symbol)
    }
  }

  async getHistoricalData(
    symbol: string, 
    assetType: 'stock' | 'crypto',
    period: string = '30d'
  ): Promise<HistoricalData[]> {
    if (assetType === 'crypto') {
      const days = period === '1y' ? 365 : period === '3m' ? 90 : 30
      return await this.coinGecko.getCryptoHistoricalData(symbol, days)
    } else {
      return await this.alphaVantage.getHistoricalData(symbol)
    }
  }

  // Batch quote fetching
  async getBatchQuotes(symbols: { symbol: string; assetType: 'stock' | 'crypto' }[]): Promise<(StockQuote | CryptoQuote)[]> {
    const promises = symbols.map(({ symbol, assetType }) => 
      this.getQuote(symbol, assetType).catch(error => {
        console.error(`Failed to fetch quote for ${symbol}:`, error)
        return null
      })
    )

    const results = await Promise.all(promises)
    return results.filter(Boolean) as (StockQuote | CryptoQuote)[]
  }
}

// Export singleton instance
export const marketDataService = new MarketDataService()
