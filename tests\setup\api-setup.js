// Setup for API tests
import { PrismaClient } from '@prisma/client'

// Create a test Prisma client
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: 'file:./test.db'
    }
  }
})

// Test utilities for API tests
global.testPrisma = prisma
global.testUtils = {
  // Create test user
  createTestUser: async (overrides = {}) => {
    const bcrypt = require('bcryptjs')
    const hashedPassword = await bcrypt.hash('testpassword123', 12)
    
    return await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'testuser',
        name: 'Test User',
        password: hashedPassword,
        verified: true,
        isPublic: true,
        allowCopying: true,
        ...overrides,
      },
    })
  },
  
  // Create test trade
  createTestTrade: async (userId, overrides = {}) => {
    return await prisma.trade.create({
      data: {
        userId,
        symbol: 'AAPL',
        assetType: 'STOCK',
        side: 'BUY',
        quantity: 100,
        entryPrice: 150.00,
        currentPrice: 152.50,
        status: 'OPEN',
        entryDate: new Date(),
        isPublic: true,
        ...overrides,
      },
    })
  },
  
  // Create test portfolio
  createTestPortfolio: async (userId, overrides = {}) => {
    return await prisma.portfolio.create({
      data: {
        userId,
        name: 'Test Portfolio',
        description: 'Test portfolio description',
        isDefault: true,
        isPublic: true,
        ...overrides,
      },
    })
  },
  
  // Clean up database
  cleanupDatabase: async () => {
    const tablenames = await prisma.$queryRaw`SELECT name FROM sqlite_master WHERE type='table';`
    
    for (const { name } of tablenames) {
      if (name.startsWith('_')) continue // Skip Prisma internal tables
      try {
        await prisma.$executeRawUnsafe(`DELETE FROM "${name}";`)
      } catch (error) {
        console.log(`Could not clear ${name}, probably doesn't exist.`)
      }
    }
  },
}

// Mock NextAuth session for API tests
global.mockSession = {
  user: {
    id: 'user123',
    email: '<EMAIL>',
    username: 'testuser',
    name: 'Test User',
    verified: true,
  },
  expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
}

// Setup and teardown for each test
beforeEach(async () => {
  await global.testUtils.cleanupDatabase()
})

afterAll(async () => {
  await prisma.$disconnect()
})
