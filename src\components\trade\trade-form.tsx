'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input, NumberInput } from '@/components/ui/input'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/components/ui/toaster'
import { TrendingUp, TrendingDown, DollarSign, Calendar, Tag } from 'lucide-react'
import { cn } from '@/lib/utils'

const tradeSchema = z.object({
  symbol: z.string().min(1, 'Symbol is required').max(10, 'Symbol too long'),
  assetType: z.enum(['STOCK', 'OPTION', 'CRYPTO', 'FOREX', 'FUTURE', 'ETF', 'INDEX']),
  side: z.enum(['BUY', 'SELL', 'SHORT']),
  quantity: z.number().positive('Quantity must be positive'),
  entryPrice: z.number().positive('Entry price must be positive'),
  entryDate: z.string().min(1, 'Entry date is required'),
  portfolioId: z.string().optional(),
  notes: z.string().optional(),
  tags: z.array(z.string()).optional(),
  isPublic: z.boolean().default(true),
})

type TradeFormData = z.infer<typeof tradeSchema>

interface TradeFormProps {
  onSubmit: (data: TradeFormData) => Promise<void>
  portfolios?: Array<{ id: string; name: string }>
  initialData?: Partial<TradeFormData>
  isLoading?: boolean
}

const assetTypes = [
  { value: 'STOCK', label: 'Stock' },
  { value: 'OPTION', label: 'Option' },
  { value: 'CRYPTO', label: 'Crypto' },
  { value: 'FOREX', label: 'Forex' },
  { value: 'FUTURE', label: 'Future' },
  { value: 'ETF', label: 'ETF' },
  { value: 'INDEX', label: 'Index' },
]

const tradeSides = [
  { value: 'BUY', label: 'Buy', icon: TrendingUp, color: 'text-profit' },
  { value: 'SELL', label: 'Sell', icon: TrendingDown, color: 'text-loss' },
  { value: 'SHORT', label: 'Short', icon: TrendingDown, color: 'text-orange-500' },
]

export function TradeForm({ 
  onSubmit, 
  portfolios = [], 
  initialData, 
  isLoading = false 
}: TradeFormProps) {
  const [tags, setTags] = useState<string[]>(initialData?.tags || [])
  const [tagInput, setTagInput] = useState('')
  const { toast } = useToast()

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<TradeFormData>({
    resolver: zodResolver(tradeSchema),
    defaultValues: {
      entryDate: new Date().toISOString().split('T')[0],
      isPublic: true,
      ...initialData,
    },
  })

  const watchedSide = watch('side')
  const watchedQuantity = watch('quantity')
  const watchedPrice = watch('entryPrice')

  const totalValue = (watchedQuantity || 0) * (watchedPrice || 0)

  const handleFormSubmit = async (data: TradeFormData) => {
    try {
      await onSubmit({ ...data, tags })
      toast.success('Trade created successfully!')
    } catch (error) {
      toast.error('Failed to create trade', error instanceof Error ? error.message : undefined)
    }
  }

  const addTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      const newTags = [...tags, tagInput.trim()]
      setTags(newTags)
      setTagInput('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      addTag()
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <DollarSign className="h-5 w-5 text-primary" />
          <span>New Trade</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Symbol and Asset Type */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Symbol"
              placeholder="AAPL, BTC, EUR/USD..."
              error={errors.symbol?.message}
              {...register('symbol')}
              className="uppercase"
            />
            
            <div className="space-y-2">
              <Label>Asset Type</Label>
              <Select onValueChange={(value) => setValue('assetType', value as any)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select asset type" />
                </SelectTrigger>
                <SelectContent>
                  {assetTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.assetType && (
                <p className="text-sm text-destructive">{errors.assetType.message}</p>
              )}
            </div>
          </div>

          {/* Trade Side */}
          <div className="space-y-2">
            <Label>Trade Side</Label>
            <div className="grid grid-cols-3 gap-2">
              {tradeSides.map((side) => {
                const Icon = side.icon
                const isSelected = watchedSide === side.value
                
                return (
                  <button
                    key={side.value}
                    type="button"
                    onClick={() => setValue('side', side.value as any)}
                    className={cn(
                      'flex items-center justify-center space-x-2 p-3 rounded-lg border transition-all',
                      isSelected 
                        ? 'border-primary bg-primary/10 text-primary' 
                        : 'border-input hover:border-primary/50'
                    )}
                  >
                    <Icon className={cn('h-4 w-4', isSelected ? 'text-primary' : side.color)} />
                    <span className="font-medium">{side.label}</span>
                  </button>
                )
              })}
            </div>
            {errors.side && (
              <p className="text-sm text-destructive">{errors.side.message}</p>
            )}
          </div>

          {/* Quantity and Price */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <NumberInput
              label="Quantity"
              placeholder="100"
              min={0}
              step={0.01}
              precision={8}
              error={errors.quantity?.message}
              onChange={(value) => setValue('quantity', value || 0)}
            />
            
            <NumberInput
              label="Entry Price"
              placeholder="150.00"
              min={0}
              step={0.01}
              precision={2}
              prefix="$"
              error={errors.entryPrice?.message}
              onChange={(value) => setValue('entryPrice', value || 0)}
            />
          </div>

          {/* Total Value Display */}
          {totalValue > 0 && (
            <div className="p-3 bg-muted/50 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Total Value:</span>
                <span className="font-semibold text-lg">${totalValue.toFixed(2)}</span>
              </div>
            </div>
          )}

          {/* Entry Date and Portfolio */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Entry Date"
              type="date"
              error={errors.entryDate?.message}
              leftIcon={<Calendar className="h-4 w-4" />}
              {...register('entryDate')}
            />
            
            {portfolios.length > 0 && (
              <div className="space-y-2">
                <Label>Portfolio (Optional)</Label>
                <Select onValueChange={(value) => setValue('portfolioId', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select portfolio" />
                  </SelectTrigger>
                  <SelectContent>
                    {portfolios.map((portfolio) => (
                      <SelectItem key={portfolio.id} value={portfolio.id}>
                        {portfolio.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          {/* Tags */}
          <div className="space-y-2">
            <Label>Tags</Label>
            <div className="flex flex-wrap gap-2 mb-2">
              {tags.map((tag) => (
                <Badge key={tag} variant="secondary" className="cursor-pointer">
                  {tag}
                  <button
                    type="button"
                    onClick={() => removeTag(tag)}
                    className="ml-1 text-xs hover:text-destructive"
                  >
                    ×
                  </button>
                </Badge>
              ))}
            </div>
            <div className="flex space-x-2">
              <Input
                placeholder="Add tag..."
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyPress={handleKeyPress}
                leftIcon={<Tag className="h-4 w-4" />}
              />
              <Button type="button" variant="outline" onClick={addTag}>
                Add
              </Button>
            </div>
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label>Notes (Optional)</Label>
            <Textarea
              placeholder="Trade rationale, strategy, or any additional notes..."
              rows={3}
              {...register('notes')}
            />
          </div>

          {/* Privacy Setting */}
          <div className="flex items-center justify-between p-3 border rounded-lg">
            <div>
              <Label>Make trade public</Label>
              <p className="text-sm text-muted-foreground">
                Allow other users to see this trade
              </p>
            </div>
            <Switch
              checked={watch('isPublic')}
              onCheckedChange={(checked) => setValue('isPublic', checked)}
            />
          </div>

          {/* Submit Button */}
          <Button
            type="submit"
            className="w-full"
            size="lg"
            loading={isSubmitting || isLoading}
          >
            Create Trade
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
