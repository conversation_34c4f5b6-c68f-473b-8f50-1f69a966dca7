{"name": "tradeflow-pro", "version": "1.0.0", "description": "Social Trading Platform - Track, Share, and Copy Trades", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@next/font": "^14.0.4", "@prisma/client": "^5.7.1", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^5.17.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^3.0.6", "framer-motion": "^10.16.16", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.303.0", "next": "^14.0.4", "next-auth": "^4.24.5", "next-themes": "^0.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "recharts": "^2.8.0", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "jest": "^29.7.0", "postcss": "^8.4.32", "prisma": "^5.7.1", "tailwindcss": "^3.4.0", "tsx": "^4.20.3", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}, "keywords": ["trading", "social-trading", "stocks", "crypto", "investment", "portfolio", "nextjs", "react", "typescript"], "author": "TradeFlow Pro Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/tradeflow-pro.git"}, "bugs": {"url": "https://github.com/yourusername/tradeflow-pro/issues"}, "homepage": "https://tradeflowpro.com", "prisma": {"seed": "tsx prisma/seed.ts"}}