// Global setup for all tests
const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

module.exports = async () => {
  console.log('🧪 Setting up test environment...')
  
  // Ensure test database is clean
  const testDbPath = path.join(process.cwd(), 'prisma', 'test.db')
  if (fs.existsSync(testDbPath)) {
    fs.unlinkSync(testDbPath)
  }
  
  // Set test environment
  process.env.DATABASE_URL = 'file:./test.db'
  process.env.NODE_ENV = 'test'
  
  try {
    // Generate Prisma client for test environment
    execSync('npx prisma generate', { stdio: 'inherit' })
    
    // Push database schema to test database
    execSync('npx prisma db push --force-reset', { 
      stdio: 'inherit',
      env: { ...process.env, DATABASE_URL: 'file:./test.db' }
    })
    
    console.log('✅ Test database setup complete')
  } catch (error) {
    console.error('❌ Test database setup failed:', error)
    throw error
  }
}
