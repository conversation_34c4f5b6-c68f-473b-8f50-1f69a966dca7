@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Light theme colors */
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
  --radius: 0.5rem;
  
  /* Trading specific colors */
  --profit: 142 76% 36%;
  --loss: 0 84% 60%;
  --neutral: 215 16% 47%;
}

.dark {
  /* Dark theme colors */
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 224.3 76.3% 94.1%;
  
  /* Trading specific colors for dark mode */
  --profit: 142 70% 45%;
  --loss: 0 84% 60%;
  --neutral: 215 20% 65%;
}

* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: 'Inter', sans-serif;
  font-feature-settings: "rlig" 1, "calt" 1;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted-foreground) / 0.3) hsl(var(--muted));
}

/* Trading specific utility classes */
.text-profit {
  color: hsl(var(--profit));
}

.text-loss {
  color: hsl(var(--loss));
}

.text-neutral {
  color: hsl(var(--neutral));
}

.bg-profit {
  background-color: hsl(var(--profit));
}

.bg-loss {
  background-color: hsl(var(--loss));
}

.bg-neutral {
  background-color: hsl(var(--neutral));
}

.border-profit {
  border-color: hsl(var(--profit) / 0.2);
}

.border-loss {
  border-color: hsl(var(--loss) / 0.2);
}

.border-neutral {
  border-color: hsl(var(--neutral) / 0.2);
}

/* Animation utilities */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 1.5s infinite;
}

@keyframes pulse-profit {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
    background-color: hsl(var(--profit) / 0.1);
  }
}

@keyframes pulse-loss {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
    background-color: hsl(var(--loss) / 0.1);
  }
}

.animate-pulse-profit {
  animation: pulse-profit 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-pulse-loss {
  animation: pulse-loss 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Grid background pattern */
.bg-grid-pattern {
  background-image: radial-gradient(circle, hsl(var(--muted-foreground) / 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Focus styles for accessibility */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
}

/* Loading states */
.skeleton {
  @apply animate-pulse bg-muted rounded;
}

/* Card hover effects */
.card-hover {
  @apply transition-all duration-200 hover:shadow-md hover:scale-[1.02];
}

.card-interactive {
  @apply cursor-pointer transition-all duration-200 hover:shadow-lg hover:border-primary/20;
}

/* Button loading state */
.btn-loading {
  @apply relative overflow-hidden;
}

.btn-loading::after {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent;
  animation: shimmer 1.5s infinite;
  transform: translateX(-100%);
}

/* Form styles */
.form-field {
  @apply space-y-2;
}

.form-label {
  @apply text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70;
}

.form-input {
  @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
}

.form-error {
  @apply text-sm text-destructive;
}

/* Chart styles */
.chart-container {
  @apply relative w-full h-full overflow-hidden rounded-lg border bg-card;
}

.chart-tooltip {
  @apply bg-popover border rounded-md shadow-md p-2 text-sm;
}

/* Mobile optimizations */
.mobile-safe-area {
  padding-bottom: env(safe-area-inset-bottom);
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --border: 0 0% 20%;
    --input: 0 0% 20%;
  }
  
  .dark {
    --border: 0 0% 80%;
    --input: 0 0% 80%;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
