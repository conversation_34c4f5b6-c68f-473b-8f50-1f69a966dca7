@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }

  /* Firefox scrollbar */
  * {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground) / 0.3) hsl(var(--muted));
  }
}

@layer components {
  /* Trading specific components */
  .profit-text {
    @apply text-profit font-medium;
  }

  .loss-text {
    @apply text-loss font-medium;
  }

  .neutral-text {
    @apply text-neutral font-medium;
  }

  .profit-bg {
    @apply bg-profit/10 text-profit;
  }

  .loss-bg {
    @apply bg-loss/10 text-loss;
  }

  .neutral-bg {
    @apply bg-neutral/10 text-neutral;
  }

  /* Card variants */
  .card-hover {
    @apply transition-all duration-200 hover:shadow-md hover:scale-[1.02];
  }

  .card-interactive {
    @apply cursor-pointer transition-all duration-200 hover:shadow-lg hover:border-primary/20;
  }

  /* Button loading state */
  .btn-loading {
    @apply relative overflow-hidden;
  }

  .btn-loading::after {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent;
    animation: shimmer 1.5s infinite;
    transform: translateX(-100%);
  }

  /* Grid background pattern */
  .bg-grid-white\/\[0\.02\] {
    background-image: radial-gradient(circle, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  }

  .bg-grid-black\/\[0\.02\] {
    background-image: radial-gradient(circle, rgba(0, 0, 0, 0.02) 1px, transparent 1px);
  }

  /* Trading chart styles */
  .chart-container {
    @apply relative w-full h-full overflow-hidden rounded-lg border bg-card;
  }

  .chart-tooltip {
    @apply bg-popover border rounded-md shadow-md p-2 text-sm;
  }

  /* Form styles */
  .form-field {
    @apply space-y-2;
  }

  .form-label {
    @apply text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70;
  }

  .form-input {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .form-error {
    @apply text-sm text-destructive;
  }

  /* Loading states */
  .skeleton {
    @apply animate-pulse bg-muted rounded;
  }

  .pulse-profit {
    @apply animate-pulse-profit;
  }

  .pulse-loss {
    @apply animate-pulse-loss;
  }

  /* Mobile optimizations */
  .mobile-safe-area {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Focus styles for accessibility */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }
}

@layer utilities {
  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }

  /* Animation utilities */
  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  .animate-shimmer {
    animation: shimmer 1.5s infinite;
  }

  /* Gradient utilities */
  .gradient-profit {
    @apply bg-gradient-to-r from-profit to-profit/80;
  }

  .gradient-loss {
    @apply bg-gradient-to-r from-loss to-loss/80;
  }

  .gradient-primary {
    @apply bg-gradient-to-r from-primary to-primary/80;
  }

  /* Shadow utilities */
  .shadow-profit {
    box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.15);
  }

  .shadow-loss {
    box-shadow: 0 4px 14px 0 rgba(239, 68, 68, 0.15);
  }

  /* Border utilities */
  .border-profit {
    @apply border-profit/20;
  }

  .border-loss {
    @apply border-loss/20;
  }

  /* Backdrop utilities */
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --border: 0 0% 20%;
    --input: 0 0% 20%;
  }
  
  .dark {
    --border: 0 0% 80%;
    --input: 0 0% 80%;
  }
}
