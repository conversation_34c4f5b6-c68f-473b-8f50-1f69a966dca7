import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { marketDataService } from '@/lib/api/market-data'
import { z } from 'zod'

// Validation schema for trade creation
const createTradeSchema = z.object({
  symbol: z.string().min(1).max(10),
  assetType: z.enum(['STOCK', 'OPTION', 'CRYPTO', 'FOREX', 'FUTURE', 'ETF', 'INDEX']),
  side: z.enum(['BUY', 'SELL', 'SHORT']),
  quantity: z.number().positive(),
  entryPrice: z.number().positive(),
  entryDate: z.string().datetime(),
  portfolioId: z.string().optional(),
  notes: z.string().optional(),
  tags: z.array(z.string()).optional(),
  isPublic: z.boolean().default(true),
})

// GET /api/trades - Get user's trades
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status')
    const assetType = searchParams.get('assetType')
    const portfolioId = searchParams.get('portfolioId')

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {
      userId: session.user.id,
    }

    if (status) {
      where.status = status
    }

    if (assetType) {
      where.assetType = assetType
    }

    if (portfolioId) {
      where.portfolioId = portfolioId
    }

    // Get trades with pagination
    const [trades, total] = await Promise.all([
      prisma.trade.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          portfolio: {
            select: { name: true }
          },
          events: {
            orderBy: { createdAt: 'desc' }
          },
          _count: {
            select: {
              likes: true,
              comments: true,
              copiedTrades: true,
            }
          }
        }
      }),
      prisma.trade.count({ where })
    ])

    // Calculate current prices and P&L for open trades
    const openTrades = trades.filter(trade => trade.status === 'OPEN')
    if (openTrades.length > 0) {
      const symbols = openTrades.map(trade => ({
        symbol: trade.symbol,
        assetType: trade.assetType === 'CRYPTO' ? 'crypto' as const : 'stock' as const
      }))

      try {
        const quotes = await marketDataService.getBatchQuotes(symbols)
        
        // Update trades with current prices
        for (const trade of openTrades) {
          const quote = quotes.find(q => q.symbol === trade.symbol)
          if (quote) {
            trade.currentPrice = quote.price
            
            // Calculate unrealized P&L
            const priceDiff = quote.price - Number(trade.entryPrice)
            const multiplier = trade.side === 'SHORT' ? -1 : 1
            trade.unrealizedPnl = priceDiff * Number(trade.quantity) * multiplier
          }
        }
      } catch (error) {
        console.error('Error fetching market data:', error)
      }
    }

    return NextResponse.json({
      trades,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      }
    })

  } catch (error) {
    console.error('Error fetching trades:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/trades - Create new trade
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createTradeSchema.parse(body)

    // Get current market price for validation
    let currentPrice: number
    try {
      const assetType = validatedData.assetType === 'CRYPTO' ? 'crypto' : 'stock'
      const quote = await marketDataService.getQuote(validatedData.symbol, assetType)
      currentPrice = quote.price
    } catch (error) {
      console.error('Error fetching market data:', error)
      currentPrice = validatedData.entryPrice // Fallback to entry price
    }

    // Create trade
    const trade = await prisma.trade.create({
      data: {
        userId: session.user.id,
        symbol: validatedData.symbol.toUpperCase(),
        assetType: validatedData.assetType,
        side: validatedData.side,
        quantity: validatedData.quantity,
        entryPrice: validatedData.entryPrice,
        currentPrice,
        entryDate: new Date(validatedData.entryDate),
        portfolioId: validatedData.portfolioId,
        notes: validatedData.notes,
        tags: validatedData.tags || [],
        isPublic: validatedData.isPublic,
        status: 'OPEN',
      },
      include: {
        portfolio: {
          select: { name: true }
        },
        user: {
          select: { username: true, name: true, avatar: true }
        }
      }
    })

    // Create initial trade event
    await prisma.tradeEvent.create({
      data: {
        tradeId: trade.id,
        type: 'OPEN',
        quantity: validatedData.quantity,
        price: validatedData.entryPrice,
        notes: validatedData.notes,
      }
    })

    // TODO: Emit socket event for real-time updates
    // socketService.emit('trade-created', { trade, userId: session.user.id })

    return NextResponse.json(trade, { status: 201 })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating trade:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
