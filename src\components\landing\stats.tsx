'use client'

import { motion } from 'framer-motion'
import { useEffect, useState } from 'react'

const stats = [
  {
    id: 'users',
    label: 'Active Traders',
    value: 50000,
    suffix: '+',
    description: 'Traders using our platform daily',
    color: 'text-primary',
  },
  {
    id: 'trades',
    label: 'Trades Tracked',
    value: 2000000,
    suffix: '+',
    description: 'Total trades tracked and analyzed',
    color: 'text-profit',
  },
  {
    id: 'success',
    label: 'Average Success Rate',
    value: 87,
    suffix: '%',
    description: 'Success rate of our top traders',
    color: 'text-blue-500',
  },
  {
    id: 'volume',
    label: 'Trading Volume',
    value: 500,
    suffix: 'M+',
    description: 'Monthly trading volume tracked',
    color: 'text-purple-500',
  },
]

function AnimatedCounter({ 
  value, 
  duration = 2000 
}: { 
  value: number
  duration?: number 
}) {
  const [count, setCount] = useState(0)

  useEffect(() => {
    let startTime: number
    let animationFrame: number

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime
      const progress = Math.min((currentTime - startTime) / duration, 1)
      
      // Easing function for smooth animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4)
      setCount(Math.floor(value * easeOutQuart))

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate)
      }
    }

    animationFrame = requestAnimationFrame(animate)

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame)
      }
    }
  }, [value, duration])

  return <span>{count.toLocaleString()}</span>
}

export function Stats() {
  return (
    <section className="py-24 bg-gradient-to-br from-background to-primary/5">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-4">
            Trusted by{' '}
            <span className="bg-gradient-to-r from-primary to-profit bg-clip-text text-transparent">
              Thousands
            </span>{' '}
            of Traders
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Join a growing community of successful traders who trust TradeFlow Pro 
            to track, analyze, and improve their trading performance.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="text-center group"
            >
              <div className="relative p-8 bg-card border rounded-2xl shadow-sm hover:shadow-lg transition-all duration-300 hover:border-primary/20">
                {/* Background decoration */}
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                
                <div className="relative">
                  <div className={`text-4xl sm:text-5xl font-bold ${stat.color} mb-2`}>
                    <AnimatedCounter value={stat.value} />
                    <span className="text-3xl sm:text-4xl">{stat.suffix}</span>
                  </div>
                  
                  <h3 className="text-lg font-semibold text-foreground mb-2">
                    {stat.label}
                  </h3>
                  
                  <p className="text-sm text-muted-foreground">
                    {stat.description}
                  </p>
                </div>

                {/* Floating elements for visual interest */}
                {index === 0 && (
                  <motion.div
                    animate={{ 
                      y: [0, -10, 0],
                      rotate: [0, 5, 0]
                    }}
                    transition={{ 
                      duration: 4, 
                      repeat: Infinity, 
                      ease: "easeInOut" 
                    }}
                    className="absolute -top-2 -right-2 w-6 h-6 bg-primary/20 rounded-full"
                  />
                )}
                
                {index === 1 && (
                  <motion.div
                    animate={{ 
                      y: [0, 10, 0],
                      rotate: [0, -5, 0]
                    }}
                    transition={{ 
                      duration: 3, 
                      repeat: Infinity, 
                      ease: "easeInOut",
                      delay: 1
                    }}
                    className="absolute -bottom-2 -left-2 w-4 h-4 bg-profit/20 rounded-full"
                  />
                )}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Additional metrics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8"
        >
          <div className="text-center">
            <div className="text-2xl font-bold text-foreground mb-2">24/7</div>
            <div className="text-sm text-muted-foreground">Real-time market data</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-foreground mb-2">150+</div>
            <div className="text-sm text-muted-foreground">Supported exchanges</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-foreground mb-2">99.9%</div>
            <div className="text-sm text-muted-foreground">Platform uptime</div>
          </div>
        </motion.div>

        {/* Trust indicators */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="mt-16 flex flex-wrap justify-center items-center gap-8 opacity-60"
        >
          <div className="text-sm text-muted-foreground">Trusted by traders from</div>
          <div className="flex items-center space-x-6">
            <span className="text-sm font-medium">🇺🇸 USA</span>
            <span className="text-sm font-medium">🇬🇧 UK</span>
            <span className="text-sm font-medium">🇨🇦 Canada</span>
            <span className="text-sm font-medium">🇦🇺 Australia</span>
            <span className="text-sm font-medium">🇩🇪 Germany</span>
            <span className="text-sm font-medium">🇯🇵 Japan</span>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
