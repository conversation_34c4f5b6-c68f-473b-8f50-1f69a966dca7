import { NextRequest } from 'next/server'
import { GET, POST } from '../../src/app/api/trades/route'
import { getServerSession } from 'next-auth'

// Mock the dependencies
jest.mock('next-auth', () => ({
  getServerSession: jest.fn(),
}))

jest.mock('../../src/lib/auth', () => ({
  authOptions: {},
}))

jest.mock('../../src/lib/db', () => ({
  prisma: {
    trade: {
      findMany: jest.fn(),
      count: jest.fn(),
      create: jest.fn(),
    },
    tradeEvent: {
      create: jest.fn(),
    },
  },
}))

jest.mock('../../src/lib/api/market-data', () => ({
  marketDataService: {
    getBatchQuotes: jest.fn(),
    getQuote: jest.fn(),
  },
}))

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>

describe('/api/trades', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET /api/trades', () => {
    it('should return 401 when user is not authenticated', async () => {
      mockGetServerSession.mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/trades')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Unauthorized')
    })

    it('should return trades for authenticated user', async () => {
      const mockSession = {
        user: { id: 'user123', email: '<EMAIL>' },
      }
      mockGetServerSession.mockResolvedValue(mockSession)

      const mockTrades = [
        {
          id: 'trade1',
          userId: 'user123',
          symbol: 'AAPL',
          assetType: 'STOCK',
          side: 'BUY',
          quantity: 100,
          entryPrice: 150.00,
          status: 'OPEN',
          createdAt: new Date(),
          portfolio: { name: 'Main Portfolio' },
          events: [],
          _count: { likes: 0, comments: 0, copiedTrades: 0 },
        },
      ]

      const { prisma } = require('../../src/lib/db')
      prisma.trade.findMany.mockResolvedValue(mockTrades)
      prisma.trade.count.mockResolvedValue(1)

      const request = new NextRequest('http://localhost:3000/api/trades')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.trades).toEqual(mockTrades)
      expect(data.pagination).toEqual({
        page: 1,
        limit: 10,
        total: 1,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      })
    })

    it('should handle pagination parameters', async () => {
      const mockSession = {
        user: { id: 'user123', email: '<EMAIL>' },
      }
      mockGetServerSession.mockResolvedValue(mockSession)

      const { prisma } = require('../../src/lib/db')
      prisma.trade.findMany.mockResolvedValue([])
      prisma.trade.count.mockResolvedValue(25)

      const request = new NextRequest('http://localhost:3000/api/trades?page=2&limit=5')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(prisma.trade.findMany).toHaveBeenCalledWith({
        where: { userId: 'user123' },
        skip: 5, // (page 2 - 1) * limit 5
        take: 5,
        orderBy: { createdAt: 'desc' },
        include: expect.any(Object),
      })
      expect(data.pagination).toEqual({
        page: 2,
        limit: 5,
        total: 25,
        totalPages: 5,
        hasNext: true,
        hasPrev: true,
      })
    })

    it('should filter trades by status', async () => {
      const mockSession = {
        user: { id: 'user123', email: '<EMAIL>' },
      }
      mockGetServerSession.mockResolvedValue(mockSession)

      const { prisma } = require('../../src/lib/db')
      prisma.trade.findMany.mockResolvedValue([])
      prisma.trade.count.mockResolvedValue(0)

      const request = new NextRequest('http://localhost:3000/api/trades?status=CLOSED')
      await GET(request)

      expect(prisma.trade.findMany).toHaveBeenCalledWith({
        where: { userId: 'user123', status: 'CLOSED' },
        skip: 0,
        take: 10,
        orderBy: { createdAt: 'desc' },
        include: expect.any(Object),
      })
    })

    it('should update current prices for open trades', async () => {
      const mockSession = {
        user: { id: 'user123', email: '<EMAIL>' },
      }
      mockGetServerSession.mockResolvedValue(mockSession)

      const mockTrades = [
        {
          id: 'trade1',
          symbol: 'AAPL',
          assetType: 'STOCK',
          side: 'BUY',
          quantity: 100,
          entryPrice: 150.00,
          status: 'OPEN',
          currentPrice: null,
          unrealizedPnl: null,
        },
      ]

      const { prisma } = require('../../src/lib/db')
      const { marketDataService } = require('../../src/lib/api/market-data')
      
      prisma.trade.findMany.mockResolvedValue(mockTrades)
      prisma.trade.count.mockResolvedValue(1)
      marketDataService.getBatchQuotes.mockResolvedValue([
        { symbol: 'AAPL', price: 155.00 },
      ])

      const request = new NextRequest('http://localhost:3000/api/trades')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.trades[0].currentPrice).toBe(155.00)
      expect(data.trades[0].unrealizedPnl).toBe(500.00) // (155 - 150) * 100
    })
  })

  describe('POST /api/trades', () => {
    it('should return 401 when user is not authenticated', async () => {
      mockGetServerSession.mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/trades', {
        method: 'POST',
        body: JSON.stringify({}),
      })
      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Unauthorized')
    })

    it('should create a new trade with valid data', async () => {
      const mockSession = {
        user: { id: 'user123', email: '<EMAIL>' },
      }
      mockGetServerSession.mockResolvedValue(mockSession)

      const tradeData = {
        symbol: 'AAPL',
        assetType: 'STOCK',
        side: 'BUY',
        quantity: 100,
        entryPrice: 150.00,
        entryDate: new Date().toISOString(),
        isPublic: true,
      }

      const mockCreatedTrade = {
        id: 'trade123',
        userId: 'user123',
        ...tradeData,
        status: 'OPEN',
        currentPrice: 150.00,
        createdAt: new Date(),
        updatedAt: new Date(),
        portfolio: null,
        user: {
          username: 'testuser',
          name: 'Test User',
          avatar: null,
        },
      }

      const { prisma } = require('../../src/lib/db')
      const { marketDataService } = require('../../src/lib/api/market-data')
      
      prisma.trade.create.mockResolvedValue(mockCreatedTrade)
      prisma.tradeEvent.create.mockResolvedValue({})
      marketDataService.getQuote.mockResolvedValue({ price: 150.00 })

      const request = new NextRequest('http://localhost:3000/api/trades', {
        method: 'POST',
        body: JSON.stringify(tradeData),
      })
      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.id).toBe('trade123')
      expect(data.symbol).toBe('AAPL')
      expect(prisma.trade.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          userId: 'user123',
          symbol: 'AAPL',
          assetType: 'STOCK',
          side: 'BUY',
          quantity: 100,
          entryPrice: 150.00,
          status: 'OPEN',
        }),
        include: expect.any(Object),
      })
      expect(prisma.tradeEvent.create).toHaveBeenCalled()
    })

    it('should return 400 for invalid trade data', async () => {
      const mockSession = {
        user: { id: 'user123', email: '<EMAIL>' },
      }
      mockGetServerSession.mockResolvedValue(mockSession)

      const invalidTradeData = {
        symbol: '', // Invalid: empty symbol
        assetType: 'INVALID_TYPE',
        side: 'BUY',
        quantity: -10, // Invalid: negative quantity
        entryPrice: 0, // Invalid: zero price
      }

      const request = new NextRequest('http://localhost:3000/api/trades', {
        method: 'POST',
        body: JSON.stringify(invalidTradeData),
      })
      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Validation error')
      expect(data.details).toBeDefined()
    })

    it('should handle market data service errors gracefully', async () => {
      const mockSession = {
        user: { id: 'user123', email: '<EMAIL>' },
      }
      mockGetServerSession.mockResolvedValue(mockSession)

      const tradeData = {
        symbol: 'AAPL',
        assetType: 'STOCK',
        side: 'BUY',
        quantity: 100,
        entryPrice: 150.00,
        entryDate: new Date().toISOString(),
        isPublic: true,
      }

      const { marketDataService } = require('../../src/lib/api/market-data')
      const { prisma } = require('../../src/lib/db')
      
      marketDataService.getQuote.mockRejectedValue(new Error('Market data unavailable'))
      prisma.trade.create.mockResolvedValue({ id: 'trade123', ...tradeData })
      prisma.tradeEvent.create.mockResolvedValue({})

      const request = new NextRequest('http://localhost:3000/api/trades', {
        method: 'POST',
        body: JSON.stringify(tradeData),
      })
      const response = await POST(request)

      expect(response.status).toBe(201)
      // Should use entry price as fallback when market data fails
      expect(prisma.trade.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          currentPrice: 150.00, // Falls back to entry price
        }),
        include: expect.any(Object),
      })
    })
  })
})
