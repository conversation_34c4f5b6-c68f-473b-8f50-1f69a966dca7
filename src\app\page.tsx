import { Suspense } from 'react'
import { <PERSON> } from '@/components/landing/hero'
import { Features } from '@/components/landing/features'
import { Stats } from '@/components/landing/stats'
import { Testimonials } from '@/components/landing/testimonials'
import { CTA } from '@/components/landing/cta'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { LoadingSpinner } from '@/components/ui/loading-spinner'

export default function HomePage() {
  return (
    <>
      <Header />
      <main className="flex-1">
        <Suspense fallback={<LoadingSpinner />}>
          <Hero />
        </Suspense>
        
        <Suspense fallback={<div className="h-96 animate-pulse bg-muted" />}>
          <Features />
        </Suspense>
        
        <Suspense fallback={<div className="h-64 animate-pulse bg-muted" />}>
          <Stats />
        </Suspense>
        
        <Suspense fallback={<div className="h-96 animate-pulse bg-muted" />}>
          <Testimonials />
        </Suspense>
        
        <Suspense fallback={<div className="h-64 animate-pulse bg-muted" />}>
          <CTA />
        </Suspense>
      </main>
      <Footer />
    </>
  )
}

// Add loading component for the page
export function Loading() {
  return (
    <div className="flex min-h-screen items-center justify-center">
      <LoadingSpinner size="lg" />
    </div>
  )
}
