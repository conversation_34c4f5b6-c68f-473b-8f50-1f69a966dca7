import { NextRequest } from 'next/server'
import { rateLimit, withRateLimit, rateLimitConfigs } from '@/lib/rate-limit'

// Mock Redis
jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => ({
    incr: jest.fn(),
    expire: jest.fn(),
    pipeline: jest.fn(() => ({
      incr: jest.fn(),
      expire: jest.fn(),
      exec: jest.fn(),
    })),
  }))
})

describe('Rate Limiting', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Clear memory store
    const { cleanupMemoryStore } = require('@/lib/rate-limit')
    cleanupMemoryStore()
  })

  describe('rateLimit function', () => {
    it('should allow requests within rate limit', async () => {
      const request = new NextRequest('http://localhost:3000/api/test')
      
      const result = await rateLimit(request, 'api')
      
      expect(result.success).toBe(true)
      expect(result.remaining).toBe(rateLimitConfigs.api.maxRequests - 1)
      expect(result.limit).toBe(rateLimitConfigs.api.maxRequests)
      expect(result.error).toBeUndefined()
    })

    it('should block requests exceeding rate limit', async () => {
      const request = new NextRequest('http://localhost:3000/api/test')
      
      // Make requests up to the limit
      for (let i = 0; i < rateLimitConfigs.api.maxRequests; i++) {
        await rateLimit(request, 'api')
      }
      
      // This request should be blocked
      const result = await rateLimit(request, 'api')
      
      expect(result.success).toBe(false)
      expect(result.remaining).toBe(0)
      expect(result.error).toBe(rateLimitConfigs.api.message)
    })

    it('should use different limits for different rate limit types', async () => {
      const request = new NextRequest('http://localhost:3000/api/test')
      
      // Test auth rate limit (more restrictive)
      const authResult = await rateLimit(request, 'auth')
      expect(authResult.limit).toBe(rateLimitConfigs.auth.maxRequests)
      
      // Test market rate limit
      const marketResult = await rateLimit(request, 'market')
      expect(marketResult.limit).toBe(rateLimitConfigs.market.maxRequests)
      
      // Test trades rate limit
      const tradesResult = await rateLimit(request, 'trades')
      expect(tradesResult.limit).toBe(rateLimitConfigs.trades.maxRequests)
    })

    it('should differentiate between different client IPs', async () => {
      const request1 = new NextRequest('http://localhost:3000/api/test', {
        headers: { 'x-forwarded-for': '***********' }
      })
      const request2 = new NextRequest('http://localhost:3000/api/test', {
        headers: { 'x-forwarded-for': '***********' }
      })
      
      // Exhaust rate limit for first IP
      for (let i = 0; i < rateLimitConfigs.api.maxRequests; i++) {
        await rateLimit(request1, 'api')
      }
      
      // First IP should be blocked
      const result1 = await rateLimit(request1, 'api')
      expect(result1.success).toBe(false)
      
      // Second IP should still be allowed
      const result2 = await rateLimit(request2, 'api')
      expect(result2.success).toBe(true)
    })

    it('should include user ID in rate limiting key when provided', async () => {
      const request = new NextRequest('http://localhost:3000/api/test')
      
      // Test with user ID
      const result1 = await rateLimit(request, 'api', 'user123')
      expect(result1.success).toBe(true)
      
      // Test without user ID (should be separate limit)
      const result2 = await rateLimit(request, 'api')
      expect(result2.success).toBe(true)
      expect(result2.remaining).toBe(rateLimitConfigs.api.maxRequests - 1)
    })

    it('should handle Redis errors gracefully', async () => {
      // Mock Redis to throw an error
      const Redis = require('ioredis')
      const mockRedis = new Redis()
      mockRedis.pipeline().exec.mockRejectedValue(new Error('Redis connection failed'))
      
      const request = new NextRequest('http://localhost:3000/api/test')
      
      // Should fall back to allowing the request when Redis fails
      const result = await rateLimit(request, 'api')
      expect(result.success).toBe(true)
    })
  })

  describe('withRateLimit middleware', () => {
    it('should allow requests within rate limit', async () => {
      const mockHandler = jest.fn().mockResolvedValue(
        new Response(JSON.stringify({ success: true }), { status: 200 })
      )
      
      const rateLimitedHandler = withRateLimit('api')(mockHandler)
      const request = new NextRequest('http://localhost:3000/api/test')
      
      const response = await rateLimitedHandler(request)
      
      expect(response.status).toBe(200)
      expect(mockHandler).toHaveBeenCalledWith(request, undefined)
      
      // Check rate limit headers
      expect(response.headers.get('X-RateLimit-Limit')).toBe(
        rateLimitConfigs.api.maxRequests.toString()
      )
      expect(response.headers.get('X-RateLimit-Remaining')).toBe(
        (rateLimitConfigs.api.maxRequests - 1).toString()
      )
      expect(response.headers.get('X-RateLimit-Reset')).toBeTruthy()
    })

    it('should block requests exceeding rate limit', async () => {
      const mockHandler = jest.fn().mockResolvedValue(
        new Response(JSON.stringify({ success: true }), { status: 200 })
      )
      
      const rateLimitedHandler = withRateLimit('api')(mockHandler)
      const request = new NextRequest('http://localhost:3000/api/test')
      
      // Exhaust rate limit
      for (let i = 0; i < rateLimitConfigs.api.maxRequests; i++) {
        await rateLimitedHandler(request)
      }
      
      // This request should be blocked
      const response = await rateLimitedHandler(request)
      const data = await response.json()
      
      expect(response.status).toBe(429)
      expect(data.error).toBe(rateLimitConfigs.api.message)
      expect(mockHandler).toHaveBeenCalledTimes(rateLimitConfigs.api.maxRequests)
      
      // Check rate limit headers
      expect(response.headers.get('X-RateLimit-Remaining')).toBe('0')
    })

    it('should apply correct rate limits for different endpoint types', async () => {
      const mockHandler = jest.fn().mockResolvedValue(
        new Response(JSON.stringify({ success: true }), { status: 200 })
      )
      
      // Test auth endpoint (more restrictive)
      const authHandler = withRateLimit('auth')(mockHandler)
      const request = new NextRequest('http://localhost:3000/api/auth/signin')
      
      const response = await authHandler(request)
      expect(response.headers.get('X-RateLimit-Limit')).toBe(
        rateLimitConfigs.auth.maxRequests.toString()
      )
      
      // Test market endpoint
      const marketHandler = withRateLimit('market')(mockHandler)
      const marketResponse = await marketHandler(request)
      expect(marketResponse.headers.get('X-RateLimit-Limit')).toBe(
        rateLimitConfigs.market.maxRequests.toString()
      )
    })
  })

  describe('Rate limit configurations', () => {
    it('should have appropriate limits for different endpoint types', () => {
      // Auth should be most restrictive
      expect(rateLimitConfigs.auth.maxRequests).toBeLessThan(rateLimitConfigs.api.maxRequests)
      
      // Market data should be more restrictive than general API
      expect(rateLimitConfigs.market.maxRequests).toBeLessThan(rateLimitConfigs.api.maxRequests)
      
      // Trades should be more restrictive than general API
      expect(rateLimitConfigs.trades.maxRequests).toBeLessThan(rateLimitConfigs.api.maxRequests)
      
      // All should have reasonable time windows
      expect(rateLimitConfigs.auth.windowMs).toBeGreaterThan(0)
      expect(rateLimitConfigs.api.windowMs).toBeGreaterThan(0)
      expect(rateLimitConfigs.market.windowMs).toBeGreaterThan(0)
    })

    it('should have appropriate error messages', () => {
      expect(rateLimitConfigs.auth.message).toContain('authentication')
      expect(rateLimitConfigs.market.message).toContain('Market data')
      expect(rateLimitConfigs.trades.message).toContain('trades')
      expect(rateLimitConfigs.social.message).toContain('social')
    })
  })

  describe('Memory store fallback', () => {
    it('should work when Redis is not available', async () => {
      // Ensure Redis is not available for this test
      process.env.REDIS_URL = ''
      
      const request = new NextRequest('http://localhost:3000/api/test')
      
      // Should still work with memory store
      const result1 = await rateLimit(request, 'api')
      expect(result1.success).toBe(true)
      expect(result1.remaining).toBe(rateLimitConfigs.api.maxRequests - 1)
      
      // Should track subsequent requests
      const result2 = await rateLimit(request, 'api')
      expect(result2.success).toBe(true)
      expect(result2.remaining).toBe(rateLimitConfigs.api.maxRequests - 2)
    })

    it('should clean up expired entries in memory store', async () => {
      const { cleanupMemoryStore } = require('@/lib/rate-limit')
      
      const request = new NextRequest('http://localhost:3000/api/test')
      
      // Make a request to create an entry
      await rateLimit(request, 'api')
      
      // Clean up should not affect current entries
      cleanupMemoryStore()
      
      // Should still track the request
      const result = await rateLimit(request, 'api')
      expect(result.remaining).toBe(rateLimitConfigs.api.maxRequests - 2)
    })
  })
})
