import { NextRequest, NextResponse } from 'next/server'
import { Redis } from 'ioredis'

// Redis client for rate limiting
let redis: Redis | null = null

if (process.env.REDIS_URL) {
  redis = new Redis(process.env.REDIS_URL)
}

// Rate limit configurations for different endpoints
export const rateLimitConfigs = {
  // Authentication endpoints
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5, // 5 attempts per window
    message: 'Too many authentication attempts, please try again later',
  },
  
  // General API endpoints
  api: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100, // 100 requests per minute
    message: 'Too many requests, please try again later',
  },
  
  // Market data endpoints (more restrictive due to external API costs)
  market: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 30, // 30 requests per minute
    message: 'Market data rate limit exceeded, please try again later',
  },
  
  // Social actions (follow, like, comment)
  social: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 20, // 20 social actions per minute
    message: 'Too many social actions, please slow down',
  },
  
  // Trade creation
  trades: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10, // 10 trades per minute
    message: 'Too many trades created, please wait before creating more',
  },
  
  // File uploads
  upload: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 5, // 5 uploads per minute
    message: 'Upload rate limit exceeded, please try again later',
  },
}

export type RateLimitType = keyof typeof rateLimitConfigs

interface RateLimitResult {
  success: boolean
  limit: number
  remaining: number
  resetTime: number
  error?: string
}

// Get client identifier (IP + user ID if authenticated)
function getClientId(request: NextRequest, userId?: string): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const ip = forwarded ? forwarded.split(',')[0] : request.ip || 'unknown'
  return userId ? `${ip}:${userId}` : ip
}

// In-memory fallback when Redis is not available
const memoryStore = new Map<string, { count: number; resetTime: number }>()

async function incrementCounter(key: string, windowMs: number): Promise<{ count: number; resetTime: number }> {
  const now = Date.now()
  const resetTime = now + windowMs

  if (redis) {
    try {
      // Use Redis for distributed rate limiting
      const pipeline = redis.pipeline()
      pipeline.incr(key)
      pipeline.expire(key, Math.ceil(windowMs / 1000))
      const results = await pipeline.exec()
      
      const count = results?.[0]?.[1] as number || 1
      return { count, resetTime }
    } catch (error) {
      console.error('Redis rate limit error:', error)
      // Fall back to memory store
    }
  }

  // Memory store fallback
  const existing = memoryStore.get(key)
  
  if (existing && existing.resetTime > now) {
    existing.count++
    return existing
  } else {
    const newEntry = { count: 1, resetTime }
    memoryStore.set(key, newEntry)
    
    // Clean up expired entries
    setTimeout(() => {
      const current = memoryStore.get(key)
      if (current && current.resetTime <= Date.now()) {
        memoryStore.delete(key)
      }
    }, windowMs)
    
    return newEntry
  }
}

export async function rateLimit(
  request: NextRequest,
  type: RateLimitType,
  userId?: string
): Promise<RateLimitResult> {
  const config = rateLimitConfigs[type]
  const clientId = getClientId(request, userId)
  const key = `rate_limit:${type}:${clientId}`

  try {
    const { count, resetTime } = await incrementCounter(key, config.windowMs)
    
    const result: RateLimitResult = {
      success: count <= config.maxRequests,
      limit: config.maxRequests,
      remaining: Math.max(0, config.maxRequests - count),
      resetTime,
    }

    if (!result.success) {
      result.error = config.message
    }

    return result
  } catch (error) {
    console.error('Rate limiting error:', error)
    // On error, allow the request to proceed
    return {
      success: true,
      limit: config.maxRequests,
      remaining: config.maxRequests,
      resetTime: Date.now() + config.windowMs,
    }
  }
}

// Middleware wrapper for API routes
export function withRateLimit(type: RateLimitType) {
  return function rateLimitMiddleware(
    handler: (request: NextRequest, context?: any) => Promise<NextResponse>
  ) {
    return async function (request: NextRequest, context?: any): Promise<NextResponse> {
      // Extract user ID from session if available
      let userId: string | undefined
      try {
        // This would need to be implemented based on your auth system
        // For now, we'll extract from headers or skip user-specific limiting
        const authHeader = request.headers.get('authorization')
        if (authHeader) {
          // Extract user ID from JWT or session
          // userId = extractUserIdFromAuth(authHeader)
        }
      } catch (error) {
        // Continue without user ID
      }

      const rateLimitResult = await rateLimit(request, type, userId)

      // Add rate limit headers
      const response = rateLimitResult.success
        ? await handler(request, context)
        : NextResponse.json(
            { error: rateLimitResult.error },
            { status: 429 }
          )

      // Add rate limit headers to response
      response.headers.set('X-RateLimit-Limit', rateLimitResult.limit.toString())
      response.headers.set('X-RateLimit-Remaining', rateLimitResult.remaining.toString())
      response.headers.set('X-RateLimit-Reset', rateLimitResult.resetTime.toString())

      return response
    }
  }
}

// Specific rate limit functions for common use cases
export const authRateLimit = (request: NextRequest, userId?: string) =>
  rateLimit(request, 'auth', userId)

export const apiRateLimit = (request: NextRequest, userId?: string) =>
  rateLimit(request, 'api', userId)

export const marketRateLimit = (request: NextRequest, userId?: string) =>
  rateLimit(request, 'market', userId)

export const socialRateLimit = (request: NextRequest, userId?: string) =>
  rateLimit(request, 'social', userId)

export const tradesRateLimit = (request: NextRequest, userId?: string) =>
  rateLimit(request, 'trades', userId)

// Utility function to check if rate limit is exceeded
export async function isRateLimited(
  request: NextRequest,
  type: RateLimitType,
  userId?: string
): Promise<boolean> {
  const result = await rateLimit(request, type, userId)
  return !result.success
}

// Clean up function for memory store (call periodically)
export function cleanupMemoryStore(): void {
  const now = Date.now()
  for (const [key, value] of memoryStore.entries()) {
    if (value.resetTime <= now) {
      memoryStore.delete(key)
    }
  }
}

// Set up periodic cleanup if using memory store
if (!redis) {
  setInterval(cleanupMemoryStore, 5 * 60 * 1000) // Clean up every 5 minutes
}
